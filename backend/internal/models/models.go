package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID        int       `json:"id" db:"id"`
	Username  string    `json:"username" db:"username"`
	Password  string    `json:"-" db:"password"` // 不在JSON中显示密码
	Email     string    `json:"email" db:"email"`
	Avatar    string    `json:"avatar" db:"avatar"`
	Status    string    `json:"status" db:"status"` // pending, approved, rejected
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// Diary 日记模型
type Diary struct {
	ID        int       `json:"id" db:"id"`
	UserID    int       `json:"user_id" db:"user_id"`
	Title     string    `json:"title" db:"title"`
	Content   string    `json:"content" db:"content"`
	Mood      string    `json:"mood" db:"mood"`             // 心情标签
	Weather   string    `json:"weather" db:"weather"`       // 天气
	Location  string    `json:"location" db:"location"`     // 地点
	IsPrivate bool      `json:"is_private" db:"is_private"` // 是否私密
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// MediaFile 媒体文件模型
type MediaFile struct {
	ID        int       `json:"id" db:"id"`
	DiaryID   *int      `json:"diary_id" db:"diary_id"` // 可选，关联到日记
	UserID    int       `json:"user_id" db:"user_id"`
	FileName  string    `json:"file_name" db:"file_name"`
	FilePath  string    `json:"file_path" db:"file_path"`
	FileSize  int64     `json:"file_size" db:"file_size"`
	FileType  string    `json:"file_type" db:"file_type"` // image, video
	MimeType  string    `json:"mime_type" db:"mime_type"`
	Caption   string    `json:"caption" db:"caption"` // 文件描述
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// Album 相册模型
type Album struct {
	ID          int       `json:"id" db:"id"`
	UserID      int       `json:"user_id" db:"user_id"`
	Name        string    `json:"name" db:"name"`
	Description string    `json:"description" db:"description"`
	Type        string    `json:"type" db:"type"` // photo, video, mixed
	CoverImage  string    `json:"cover_image" db:"cover_image"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
	MediaCount  int       `json:"media_count,omitempty"` // 媒体文件数量，查询时填充
}

// AlbumMedia 相册媒体关联模型
type AlbumMedia struct {
	ID      int       `json:"id" db:"id"`
	AlbumID int       `json:"album_id" db:"album_id"`
	MediaID int       `json:"media_id" db:"media_id"`
	AddedAt time.Time `json:"added_at" db:"added_at"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
}

// DiaryRequest 日记请求
type DiaryRequest struct {
	Title     string `json:"title" binding:"required"`
	Content   string `json:"content" binding:"required"`
	Mood      string `json:"mood"`
	Weather   string `json:"weather"`
	Location  string `json:"location"`
	IsPrivate bool   `json:"is_private"`
}

// AlbumRequest 相册请求
type AlbumRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Type        string `json:"type" binding:"required"` // photo, video, mixed
}

// AddMediaToAlbumRequest 添加媒体到相册请求
type AddMediaToAlbumRequest struct {
	MediaIDs []int `json:"media_ids" binding:"required"`
}

// Response 通用响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}
