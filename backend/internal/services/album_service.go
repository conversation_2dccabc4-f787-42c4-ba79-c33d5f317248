package services

import (
	"database/sql"
	"net/http"
	"strconv"

	"diary-backend/internal/models"

	"github.com/gin-gonic/gin"
)

type AlbumService struct {
	db *sql.DB
}

func NewAlbumService(db *sql.DB) *AlbumService {
	return &AlbumService{db: db}
}

// CreateAlbum 创建相册
func (s *AlbumService) CreateAlbum(c *gin.Context) {
	userID := c.GetInt("user_id")

	var req models.AlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 验证相册类型
	if req.Type != "photo" && req.Type != "video" && req.Type != "mixed" {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "相册类型必须是 photo、video 或 mixed",
		})
		return
	}

	result, err := s.db.Exec(
		"INSERT INTO albums (user_id, name, description, type) VALUES (?, ?, ?, ?)",
		userID, req.Name, req.Description, req.Type,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "创建相册失败",
		})
		return
	}

	albumID, _ := result.LastInsertId()
	c.JSON(http.StatusCreated, models.Response{
		Code:    201,
		Message: "创建成功",
		Data:    gin.H{"id": albumID},
	})
}

// GetAlbums 获取相册列表（情侣共享）
func (s *AlbumService) GetAlbums(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	albumType := c.Query("type") // photo, video, mixed

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 构建查询条件（显示所有用户的相册）
	query := `
		SELECT a.id, a.user_id, a.name, a.description, a.type, a.cover_image, 
		       a.created_at, a.updated_at, COUNT(am.media_id) as media_count
		FROM albums a
		LEFT JOIN album_media am ON a.id = am.album_id
		WHERE 1=1`
	args := []interface{}{}

	if albumType != "" {
		query += " AND a.type = ?"
		args = append(args, albumType)
	}

	query += " GROUP BY a.id ORDER BY a.created_at DESC LIMIT ? OFFSET ?"
	args = append(args, limit, offset)

	rows, err := s.db.Query(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "查询失败",
		})
		return
	}
	defer rows.Close()

	var albums []models.Album
	for rows.Next() {
		var album models.Album
		err := rows.Scan(
			&album.ID, &album.UserID, &album.Name, &album.Description,
			&album.Type, &album.CoverImage, &album.CreatedAt, &album.UpdatedAt,
			&album.MediaCount,
		)
		if err != nil {
			continue
		}
		albums = append(albums, album)
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM albums WHERE 1=1"
	countArgs := []interface{}{}

	if albumType != "" {
		countQuery += " AND type = ?"
		countArgs = append(countArgs, albumType)
	}

	var total int
	s.db.QueryRow(countQuery, countArgs...).Scan(&total)

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data: gin.H{
			"albums": albums,
			"total":  total,
			"page":   page,
			"limit":  limit,
		},
	})
}

// GetAlbum 获取单个相册详情
func (s *AlbumService) GetAlbum(c *gin.Context) {
	albumID := c.Param("id")

	var album models.Album
	err := s.db.QueryRow(`
		SELECT a.id, a.user_id, a.name, a.description, a.type, a.cover_image, 
		       a.created_at, a.updated_at, COUNT(am.media_id) as media_count
		FROM albums a
		LEFT JOIN album_media am ON a.id = am.album_id
		WHERE a.id = ?
		GROUP BY a.id`,
		albumID,
	).Scan(
		&album.ID, &album.UserID, &album.Name, &album.Description,
		&album.Type, &album.CoverImage, &album.CreatedAt, &album.UpdatedAt,
		&album.MediaCount,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: "相册不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "查询失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data:    album,
	})
}

// UpdateAlbum 更新相册
func (s *AlbumService) UpdateAlbum(c *gin.Context) {
	userID := c.GetInt("user_id")
	albumID := c.Param("id")

	var req models.AlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 验证相册类型
	if req.Type != "photo" && req.Type != "video" && req.Type != "mixed" {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "相册类型必须是 photo、video 或 mixed",
		})
		return
	}

	result, err := s.db.Exec(
		"UPDATE albums SET name = ?, description = ?, type = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?",
		req.Name, req.Description, req.Type, albumID, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "更新失败",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "相册不存在或无权限",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "更新成功",
	})
}

// DeleteAlbum 删除相册
func (s *AlbumService) DeleteAlbum(c *gin.Context) {
	userID := c.GetInt("user_id")
	albumID := c.Param("id")

	result, err := s.db.Exec(
		"DELETE FROM albums WHERE id = ? AND user_id = ?",
		albumID, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "删除失败",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "相册不存在或无权限",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "删除成功",
	})
}

// AddMediaToAlbum 添加媒体到相册
func (s *AlbumService) AddMediaToAlbum(c *gin.Context) {
	userID := c.GetInt("user_id")
	albumID := c.Param("id")

	var req models.AddMediaToAlbumRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 验证相册是否存在且属于当前用户
	var albumType string
	err := s.db.QueryRow("SELECT type FROM albums WHERE id = ? AND user_id = ?", albumID, userID).Scan(&albumType)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: "相册不存在或无权限",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "查询相册失败",
			})
		}
		return
	}

	// 开始事务
	tx, err := s.db.Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "开始事务失败",
		})
		return
	}
	defer tx.Rollback()

	successCount := 0
	for _, mediaID := range req.MediaIDs {
		// 验证媒体文件是否存在
		var fileType string
		err := tx.QueryRow("SELECT file_type FROM media_files WHERE id = ?", mediaID).Scan(&fileType)
		if err != nil {
			continue // 跳过不存在的媒体文件
		}

		// 检查媒体类型是否匹配相册类型
		if albumType == "photo" && fileType != "image" {
			continue // 照片相册只能添加图片
		}
		if albumType == "video" && fileType != "video" {
			continue // 视频相册只能添加视频
		}

		// 添加到相册（忽略重复添加的错误）
		_, err = tx.Exec(
			"INSERT OR IGNORE INTO album_media (album_id, media_id) VALUES (?, ?)",
			albumID, mediaID,
		)
		if err == nil {
			successCount++
		}
	}

	// 提交事务
	if err := tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "提交事务失败",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "添加成功",
		Data: gin.H{
			"added_count": successCount,
		},
	})
}

// RemoveMediaFromAlbum 从相册中移除媒体
func (s *AlbumService) RemoveMediaFromAlbum(c *gin.Context) {
	userID := c.GetInt("user_id")
	albumID := c.Param("id")
	mediaID := c.Param("media_id")

	// 验证相册是否存在且属于当前用户
	var count int
	err := s.db.QueryRow("SELECT COUNT(*) FROM albums WHERE id = ? AND user_id = ?", albumID, userID).Scan(&count)
	if err != nil || count == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "相册不存在或无权限",
		})
		return
	}

	result, err := s.db.Exec(
		"DELETE FROM album_media WHERE album_id = ? AND media_id = ?",
		albumID, mediaID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "移除失败",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "媒体文件不在此相册中",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "移除成功",
	})
}

// GetAlbumMedia 获取相册中的媒体文件
func (s *AlbumService) GetAlbumMedia(c *gin.Context) {
	albumID := c.Param("id")
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 获取相册中的媒体文件
	query := `
		SELECT mf.id, mf.user_id, mf.file_name, mf.file_path, mf.file_size,
		       mf.file_type, mf.mime_type, mf.caption, mf.created_at, am.added_at
		FROM media_files mf
		INNER JOIN album_media am ON mf.id = am.media_id
		WHERE am.album_id = ?
		ORDER BY am.added_at DESC
		LIMIT ? OFFSET ?`

	rows, err := s.db.Query(query, albumID, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "查询失败",
		})
		return
	}
	defer rows.Close()

	var mediaFiles []gin.H
	for rows.Next() {
		var file models.MediaFile
		var addedAt string
		err := rows.Scan(
			&file.ID, &file.UserID, &file.FileName, &file.FilePath,
			&file.FileSize, &file.FileType, &file.MimeType, &file.Caption,
			&file.CreatedAt, &addedAt,
		)
		if err != nil {
			continue
		}

		mediaFiles = append(mediaFiles, gin.H{
			"id":         file.ID,
			"user_id":    file.UserID,
			"file_name":  file.FileName,
			"file_path":  file.FilePath,
			"file_size":  file.FileSize,
			"file_type":  file.FileType,
			"mime_type":  file.MimeType,
			"caption":    file.Caption,
			"created_at": file.CreatedAt,
			"added_at":   addedAt,
		})
	}

	// 获取总数
	var total int
	s.db.QueryRow("SELECT COUNT(*) FROM album_media WHERE album_id = ?", albumID).Scan(&total)

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data: gin.H{
			"media_files": mediaFiles,
			"total":       total,
			"page":        page,
			"limit":       limit,
		},
	})
}
