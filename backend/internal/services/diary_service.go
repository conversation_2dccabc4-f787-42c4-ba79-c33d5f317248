package services

import (
	"database/sql"
	"diary-backend/internal/models"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

type DiaryService struct {
	db *sql.DB
}

func NewDiaryService(db *sql.DB) *DiaryService {
	return &DiaryService{db: db}
}

// GetDiaries 获取日记列表（情侣共享）
func (s *DiaryService) GetDiaries(c *gin.Context) {
	// 分页参数
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	limit, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON><PERSON>("limit", "10"))
	offset := (page - 1) * limit

	// 搜索参数
	keyword := c.Query("keyword")
	mood := c.Query("mood")

	// 构建查询（移除用户ID过滤，显示所有用户的日记）
	query := "SELECT id, user_id, title, content, mood, weather, location, is_private, created_at, updated_at FROM diaries WHERE 1=1"
	args := []interface{}{}

	if keyword != "" {
		query += " AND (title LIKE ? OR content LIKE ?)"
		args = append(args, "%"+keyword+"%", "%"+keyword+"%")
	}

	if mood != "" {
		query += " AND mood = ?"
		args = append(args, mood)
	}

	query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, limit, offset)

	rows, err := s.db.Query(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "查询失败",
		})
		return
	}
	defer rows.Close()

	var diaries []models.Diary
	for rows.Next() {
		var diary models.Diary
		err := rows.Scan(
			&diary.ID, &diary.UserID, &diary.Title, &diary.Content, &diary.Mood,
			&diary.Weather, &diary.Location, &diary.IsPrivate,
			&diary.CreatedAt, &diary.UpdatedAt,
		)
		if err != nil {
			continue
		}
		diaries = append(diaries, diary)
	}

	// 获取总数
	countQuery := "SELECT COUNT(*) FROM diaries WHERE 1=1"
	countArgs := []interface{}{}

	if keyword != "" {
		countQuery += " AND (title LIKE ? OR content LIKE ?)"
		countArgs = append(countArgs, "%"+keyword+"%", "%"+keyword+"%")
	}

	if mood != "" {
		countQuery += " AND mood = ?"
		countArgs = append(countArgs, mood)
	}

	var total int
	s.db.QueryRow(countQuery, countArgs...).Scan(&total)

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data: gin.H{
			"diaries": diaries,
			"total":   total,
			"page":    page,
			"limit":   limit,
		},
	})
}

// CreateDiary 创建日记
func (s *DiaryService) CreateDiary(c *gin.Context) {
	userID := c.GetInt("user_id")

	var req models.DiaryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	result, err := s.db.Exec(
		"INSERT INTO diaries (user_id, title, content, mood, weather, location, is_private) VALUES (?, ?, ?, ?, ?, ?, ?)",
		userID, req.Title, req.Content, req.Mood, req.Weather, req.Location, req.IsPrivate,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "创建日记失败",
		})
		return
	}

	diaryID, _ := result.LastInsertId()
	c.JSON(http.StatusCreated, models.Response{
		Code:    201,
		Message: "创建成功",
		Data:    gin.H{"id": diaryID},
	})
}

// GetDiary 获取单个日记（情侣共享）
func (s *DiaryService) GetDiary(c *gin.Context) {
	diaryID := c.Param("id")

	var diary models.Diary
	err := s.db.QueryRow(
		"SELECT id, user_id, title, content, mood, weather, location, is_private, created_at, updated_at FROM diaries WHERE id = ?",
		diaryID,
	).Scan(
		&diary.ID, &diary.UserID, &diary.Title, &diary.Content,
		&diary.Mood, &diary.Weather, &diary.Location, &diary.IsPrivate,
		&diary.CreatedAt, &diary.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: "日记不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "查询失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data:    diary,
	})
}

// UpdateDiary 更新日记
func (s *DiaryService) UpdateDiary(c *gin.Context) {
	userID := c.GetInt("user_id")
	diaryID := c.Param("id")

	var req models.DiaryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	result, err := s.db.Exec(
		"UPDATE diaries SET title = ?, content = ?, mood = ?, weather = ?, location = ?, is_private = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?",
		req.Title, req.Content, req.Mood, req.Weather, req.Location, req.IsPrivate, diaryID, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "更新失败",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "日记不存在",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "更新成功",
	})
}

// DeleteDiary 删除日记
func (s *DiaryService) DeleteDiary(c *gin.Context) {
	userID := c.GetInt("user_id")
	diaryID := c.Param("id")

	result, err := s.db.Exec(
		"DELETE FROM diaries WHERE id = ? AND user_id = ?",
		diaryID, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "删除失败",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "日记不存在",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "删除成功",
	})
}
