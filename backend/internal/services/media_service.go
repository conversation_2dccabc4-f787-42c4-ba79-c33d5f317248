package services

import (
	"database/sql"
	"diary-backend/internal/models"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type MediaService struct {
	db *sql.DB
}

func NewMediaService(db *sql.DB) *MediaService {
	return &MediaService{db: db}
}

// UploadFile 上传文件
func (s *MediaService) UploadFile(c *gin.Context) {
	userID := c.GetInt("user_id")

	file, header, err := c.Request.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "文件上传失败",
		})
		return
	}
	defer file.Close()

	// 检查文件类型
	allowedTypes := map[string]bool{
		"image/jpeg": true,
		"image/png":  true,
		"image/gif":  true,
		"video/mp4":  true,
		"video/mov":  true,
	}

	contentType := header.Header.Get("Content-Type")
	if !allowedTypes[contentType] {
		c.J<PERSON>(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "不支持的文件类型",
		})
		return
	}

	// 生成文件名
	ext := filepath.Ext(header.Filename)
	fileName := fmt.Sprintf("%d_%d%s", userID, time.Now().Unix(), ext)
	filePath := filepath.Join("uploads", fileName)

	// 确保上传目录存在
	if err := os.MkdirAll("uploads", 0755); err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "创建上传目录失败",
		})
		return
	}

	// 保存文件
	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "保存文件失败",
		})
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "保存文件失败",
		})
		return
	}

	// 保存文件信息到数据库
	result, err := s.db.Exec(
		"INSERT INTO media_files (user_id, file_name, file_path, file_size, file_type, mime_type) VALUES (?, ?, ?, ?, ?, ?)",
		userID, fileName, filePath, header.Size, getFileType(contentType), contentType,
	)

	if err != nil {
		// 删除已保存的文件
		os.Remove(filePath)
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "保存文件信息失败",
		})
		return
	}

	fileID, _ := result.LastInsertId()
	c.JSON(http.StatusCreated, models.Response{
		Code:    201,
		Message: "上传成功",
		Data: gin.H{
			"id":        fileID,
			"file_name": fileName,
			"file_path": filePath,
		},
	})
}

// GetMediaFiles 获取媒体文件列表（情侣共享）
func (s *MediaService) GetMediaFiles(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	fileType := c.Query("type") // image 或 video

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// 构建查询条件（移除用户ID过滤，显示所有用户的媒体文件）
	query := "SELECT id, user_id, file_name, file_path, file_size, file_type, mime_type, caption, created_at FROM media_files WHERE 1=1"
	args := []interface{}{}

	if fileType != "" {
		query += " AND file_type = ?"
		args = append(args, fileType)
	}

	query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
	args = append(args, limit, offset)

	rows, err := s.db.Query(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "查询失败",
		})
		return
	}
	defer rows.Close()

	var files []models.MediaFile
	for rows.Next() {
		var file models.MediaFile
		err := rows.Scan(
			&file.ID, &file.UserID, &file.FileName, &file.FilePath,
			&file.FileSize, &file.FileType, &file.MimeType, &file.Caption,
			&file.CreatedAt,
		)
		if err != nil {
			continue
		}
		files = append(files, file)
	}

	// 获取总数（也需要移除用户ID过滤）
	countQuery := "SELECT COUNT(*) FROM media_files WHERE 1=1"
	countArgs := []interface{}{}

	if fileType != "" {
		countQuery += " AND file_type = ?"
		countArgs = append(countArgs, fileType)
	}

	var total int
	s.db.QueryRow(countQuery, countArgs...).Scan(&total)

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data: gin.H{
			"files": files,
			"total": total,
			"page":  page,
			"limit": limit,
		},
	})
}

// GetMediaFile 获取单个媒体文件（情侣共享）
func (s *MediaService) GetMediaFile(c *gin.Context) {
	fileID := c.Param("id")

	var file models.MediaFile
	err := s.db.QueryRow(
		"SELECT id, user_id, file_name, file_path, file_size, file_type, mime_type, caption, created_at FROM media_files WHERE id = ?",
		fileID,
	).Scan(
		&file.ID, &file.UserID, &file.FileName, &file.FilePath,
		&file.FileSize, &file.FileType, &file.MimeType, &file.Caption,
		&file.CreatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: "文件不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "查询失败",
			})
		}
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data:    file,
	})
}

// DeleteMediaFile 删除媒体文件
func (s *MediaService) DeleteMediaFile(c *gin.Context) {
	userID := c.GetInt("user_id")
	fileID := c.Param("id")

	// 先获取文件信息
	var filePath string
	err := s.db.QueryRow(
		"SELECT file_path FROM media_files WHERE id = ? AND user_id = ?",
		fileID, userID,
	).Scan(&filePath)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, models.Response{
				Code:    404,
				Message: "文件不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "查询失败",
			})
		}
		return
	}

	// 删除数据库记录
	result, err := s.db.Exec(
		"DELETE FROM media_files WHERE id = ? AND user_id = ?",
		fileID, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "删除失败",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "文件不存在",
		})
		return
	}

	// 删除物理文件
	os.Remove(filePath)

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "删除成功",
	})
}

// UpdateCaption 更新文件描述
func (s *MediaService) UpdateCaption(c *gin.Context) {
	userID := c.GetInt("user_id")
	fileID := c.Param("id")

	var req struct {
		Caption string `json:"caption"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	result, err := s.db.Exec(
		"UPDATE media_files SET caption = ? WHERE id = ? AND user_id = ?",
		req.Caption, fileID, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "更新失败",
		})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "文件不存在",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "更新成功",
	})
}

// getFileType 根据MIME类型获取文件类型
func getFileType(mimeType string) string {
	if strings.HasPrefix(mimeType, "image/") {
		return "image"
	} else if strings.HasPrefix(mimeType, "video/") {
		return "video"
	}
	return "other"
}
