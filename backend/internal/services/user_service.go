package services

import (
	"database/sql"
	"diary-backend/internal/models"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

type UserService struct {
	db *sql.DB
}

var jwtSecret = []byte("your-secret-key") // 在生产环境中应该从环境变量读取

func NewUserService(db *sql.DB) *UserService {
	return &UserService{db: db}
}

// Register 用户注册
func (s *UserService) Register(c *gin.Context) {
	var req models.RegisterRequest
	if err := c.ShouldBindJ<PERSON>N(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 检查用户名是否已存在
	var count int
	err := s.db.QueryRow("SELECT COUNT(*) FROM users WHERE username = ?", req.Username).Scan(&count)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "服务器错误",
		})
		return
	}
	if count > 0 {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "用户名已存在",
		})
		return
	}

	// 检查邮箱是否已存在
	err = s.db.QueryRow("SELECT COUNT(*) FROM users WHERE email = ?", req.Email).Scan(&count)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "服务器错误",
		})
		return
	}
	if count > 0 {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "邮箱已存在",
		})
		return
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "密码加密失败",
		})
		return
	}

	// 插入用户（新用户状态为pending，需要审批）
	result, err := s.db.Exec(
		"INSERT INTO users (username, password, email, status) VALUES (?, ?, ?, ?)",
		req.Username, string(hashedPassword), req.Email, "pending",
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "创建用户失败",
		})
		return
	}

	userID, _ := result.LastInsertId()
	c.JSON(http.StatusCreated, models.Response{
		Code:    201,
		Message: "注册成功，请等待管理员审批后方可登录",
		Data:    gin.H{"user_id": userID},
	})
}

// Login 用户登录
func (s *UserService) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	// 查询用户（支持用户名或邮箱登录）
	var user models.User
	err := s.db.QueryRow(
		"SELECT id, username, password, email, status FROM users WHERE username = ? OR email = ?",
		req.Username, req.Username,
	).Scan(&user.ID, &user.Username, &user.Password, &user.Email, &user.Status)

	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusUnauthorized, models.Response{
				Code:    401,
				Message: "用户名或密码错误",
			})
		} else {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "服务器错误",
			})
		}
		return
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: "用户名或密码错误",
		})
		return
	}

	// 检查用户状态
	switch user.Status {
	case "pending":
		c.JSON(http.StatusForbidden, models.Response{
			Code:    403,
			Message: "您的账号正在等待审批，请联系管理员",
		})
		return
	case "rejected":
		c.JSON(http.StatusForbidden, models.Response{
			Code:    403,
			Message: "您的账号已被拒绝，请联系管理员",
		})
		return
	case "approved":
		// 继续登录流程
	default:
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "用户状态异常",
		})
		return
	}

	// 生成JWT token
	token, err := s.generateToken(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "生成token失败",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "登录成功",
		Data: gin.H{
			"token": token,
			"user": gin.H{
				"id":       user.ID,
				"username": user.Username,
				"email":    user.Email,
			},
		},
	})
}

// generateToken 生成JWT token
func (s *UserService) generateToken(userID int) (string, error) {
	claims := jwt.MapClaims{
		"user_id": userID,
		"exp":     time.Now().Add(time.Hour * 24 * 7).Unix(), // 7天过期
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

// AuthMiddleware JWT认证中间件
func (s *UserService) AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, models.Response{
				Code:    401,
				Message: "缺少认证token",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, models.Response{
				Code:    401,
				Message: "token格式错误",
			})
			c.Abort()
			return
		}

		// 解析token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			return jwtSecret, nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, models.Response{
				Code:    401,
				Message: "无效的token",
			})
			c.Abort()
			return
		}

		// 提取用户ID
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			if userID, ok := claims["user_id"].(float64); ok {
				c.Set("user_id", int(userID))
				c.Next()
				return
			}
		}

		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: "token解析失败",
		})
		c.Abort()
	}
}

// GetProfile 获取用户资料
func (s *UserService) GetProfile(c *gin.Context) {
	userID := c.GetInt("user_id")

	var user models.User
	err := s.db.QueryRow(
		"SELECT id, username, email, avatar, created_at FROM users WHERE id = ?",
		userID,
	).Scan(&user.ID, &user.Username, &user.Email, &user.Avatar, &user.CreatedAt)

	if err != nil {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "用户不存在",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data:    user,
	})
}

// UpdateProfile 更新用户资料
func (s *UserService) UpdateProfile(c *gin.Context) {
	userID := c.GetInt("user_id")

	var req struct {
		Email  string `json:"email"`
		Avatar string `json:"avatar"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "请求参数错误",
		})
		return
	}

	_, err := s.db.Exec(
		"UPDATE users SET email = ?, avatar = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
		req.Email, req.Avatar, userID,
	)

	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "更新失败",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "更新成功",
	})
}

// GetAllUsers 获取所有用户列表（管理功能）
func (s *UserService) GetAllUsers(c *gin.Context) {
	rows, err := s.db.Query(`
		SELECT id, username, email, avatar, status, created_at, updated_at
		FROM users
		ORDER BY created_at DESC
	`)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "获取用户列表失败",
		})
		return
	}
	defer rows.Close()

	var users []models.User
	for rows.Next() {
		var user models.User
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email,
			&user.Avatar, &user.Status, &user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			c.JSON(http.StatusInternalServerError, models.Response{
				Code:    500,
				Message: "解析用户数据失败",
			})
			return
		}
		users = append(users, user)
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "获取成功",
		Data:    map[string]interface{}{"users": users},
	})
}

// DeleteUser 删除用户（管理功能）
func (s *UserService) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}

	// 获取当前登录用户ID，防止删除自己
	currentUserID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.Response{
			Code:    401,
			Message: "未授权",
		})
		return
	}

	if userID == fmt.Sprintf("%v", currentUserID) {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "不能删除自己的账号",
		})
		return
	}

	// 检查用户是否存在
	var count int
	err := s.db.QueryRow("SELECT COUNT(*) FROM users WHERE id = ?", userID).Scan(&count)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "服务器错误",
		})
		return
	}
	if count == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "用户不存在",
		})
		return
	}

	// 开始事务，删除用户相关的所有数据
	tx, err := s.db.Begin()
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "开始事务失败",
		})
		return
	}
	defer tx.Rollback()

	// 删除用户的媒体文件记录
	_, err = tx.Exec("DELETE FROM media_files WHERE user_id = ?", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "删除用户媒体文件失败",
		})
		return
	}

	// 删除用户的日记
	_, err = tx.Exec("DELETE FROM diaries WHERE user_id = ?", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "删除用户日记失败",
		})
		return
	}

	// 删除用户
	_, err = tx.Exec("DELETE FROM users WHERE id = ?", userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "删除用户失败",
		})
		return
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "提交事务失败",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "用户删除成功",
	})
}

// ApproveUser 审批通过用户
func (s *UserService) ApproveUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}

	// 检查用户是否存在
	var count int
	err := s.db.QueryRow("SELECT COUNT(*) FROM users WHERE id = ?", userID).Scan(&count)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "服务器错误",
		})
		return
	}
	if count == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "用户不存在",
		})
		return
	}

	// 更新用户状态为approved
	_, err = s.db.Exec(
		"UPDATE users SET status = 'approved', updated_at = CURRENT_TIMESTAMP WHERE id = ?",
		userID,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "审批用户失败",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "用户审批成功",
	})
}

// RejectUser 拒绝用户
func (s *UserService) RejectUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		c.JSON(http.StatusBadRequest, models.Response{
			Code:    400,
			Message: "用户ID不能为空",
		})
		return
	}

	// 检查用户是否存在
	var count int
	err := s.db.QueryRow("SELECT COUNT(*) FROM users WHERE id = ?", userID).Scan(&count)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "服务器错误",
		})
		return
	}
	if count == 0 {
		c.JSON(http.StatusNotFound, models.Response{
			Code:    404,
			Message: "用户不存在",
		})
		return
	}

	// 更新用户状态为rejected
	_, err = s.db.Exec(
		"UPDATE users SET status = 'rejected', updated_at = CURRENT_TIMESTAMP WHERE id = ?",
		userID,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.Response{
			Code:    500,
			Message: "拒绝用户失败",
		})
		return
	}

	c.JSON(http.StatusOK, models.Response{
		Code:    200,
		Message: "用户已被拒绝",
	})
}
