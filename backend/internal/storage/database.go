package storage

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/mattn/go-sqlite3"
)

// InitDB 初始化数据库连接和表结构
func InitDB() (*sql.DB, error) {
	db, err := sql.Open("sqlite3", "./diary.db")
	if err != nil {
		return nil, err
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, err
	}

	// 创建表
	if err := createTables(db); err != nil {
		return nil, err
	}

	// 运行数据库迁移
	if err := runMigrations(db); err != nil {
		return nil, err
	}

	log.Println("Database initialized successfully")
	return db, nil
}

// createTables 创建数据库表
func createTables(db *sql.DB) error {
	// 用户表
	userTable := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username VARCHAR(50) UNIQUE NOT NULL,
		password VARCHAR(255) NOT NULL,
		email VARCHAR(100) UNIQUE NOT NULL,
		avatar VARCHAR(255) DEFAULT '',
		status VARCHAR(20) DEFAULT 'pending',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
	);`

	// 日记表
	diaryTable := `
	CREATE TABLE IF NOT EXISTS diaries (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id INTEGER NOT NULL,
		title VARCHAR(200) NOT NULL,
		content TEXT NOT NULL,
		mood VARCHAR(50) DEFAULT '',
		weather VARCHAR(50) DEFAULT '',
		location VARCHAR(100) DEFAULT '',
		is_private BOOLEAN DEFAULT FALSE,
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
	);`

	// 媒体文件表
	mediaTable := `
	CREATE TABLE IF NOT EXISTS media_files (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		diary_id INTEGER,
		user_id INTEGER NOT NULL,
		file_name VARCHAR(255) NOT NULL,
		file_path VARCHAR(500) NOT NULL,
		file_size INTEGER NOT NULL,
		file_type VARCHAR(20) NOT NULL,
		mime_type VARCHAR(100) NOT NULL,
		caption TEXT DEFAULT '',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (diary_id) REFERENCES diaries(id) ON DELETE CASCADE,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
	);`

	// 相册表
	albumTable := `
	CREATE TABLE IF NOT EXISTS albums (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		user_id INTEGER NOT NULL,
		name VARCHAR(200) NOT NULL,
		description TEXT DEFAULT '',
		type VARCHAR(20) DEFAULT 'mixed',
		cover_image VARCHAR(500) DEFAULT '',
		created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
	);`

	// 相册媒体关联表
	albumMediaTable := `
	CREATE TABLE IF NOT EXISTS album_media (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		album_id INTEGER NOT NULL,
		media_id INTEGER NOT NULL,
		added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
		FOREIGN KEY (album_id) REFERENCES albums(id) ON DELETE CASCADE,
		FOREIGN KEY (media_id) REFERENCES media_files(id) ON DELETE CASCADE,
		UNIQUE(album_id, media_id)
	);`

	tables := []string{userTable, diaryTable, mediaTable, albumTable, albumMediaTable}

	for _, table := range tables {
		if _, err := db.Exec(table); err != nil {
			return err
		}
	}

	return nil
}

// runMigrations 运行数据库迁移
func runMigrations(db *sql.DB) error {
	// 检查是否需要添加status字段
	var columnExists bool
	err := db.QueryRow(`
		SELECT COUNT(*) > 0
		FROM pragma_table_info('users')
		WHERE name = 'status'
	`).Scan(&columnExists)

	if err != nil {
		return fmt.Errorf("检查status字段失败: %v", err)
	}

	// 如果status字段不存在，则添加它
	if !columnExists {
		_, err = db.Exec("ALTER TABLE users ADD COLUMN status VARCHAR(20) DEFAULT 'approved'")
		if err != nil {
			return fmt.Errorf("添加status字段失败: %v", err)
		}
		log.Println("Added status column to users table")
	}

	return nil
}
