package api

import (
	"database/sql"
	"diary-backend/internal/services"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置API路由
func SetupRoutes(r *gin.Engine, db *sql.DB) {
	// 创建服务实例
	userService := services.NewUserService(db)
	diaryService := services.NewDiaryService(db)
	mediaService := services.NewMediaService(db)
	albumService := services.NewAlbumService(db)

	// API版本分组
	v1 := r.Group("/api/v1")

	// 用户相关路由
	auth := v1.Group("/auth")
	{
		auth.POST("/register", userService.Register)
		auth.POST("/login", userService.Login)
		auth.GET("/profile", userService.AuthMiddleware(), userService.GetProfile)
		auth.PUT("/profile", userService.AuthMiddleware(), userService.UpdateProfile)
	}

	// 用户管理路由
	users := v1.Group("/users")
	users.Use(userService.AuthMiddleware())
	{
		users.GET("", userService.GetAllUsers)
		users.DELETE("/:id", userService.DeleteUser)
		users.PUT("/:id/approve", userService.ApproveUser)
		users.PUT("/:id/reject", userService.RejectUser)
	}

	// 日记相关路由
	diaries := v1.Group("/diaries")
	diaries.Use(userService.AuthMiddleware())
	{
		diaries.GET("", diaryService.GetDiaries)
		diaries.POST("", diaryService.CreateDiary)
		diaries.GET("/:id", diaryService.GetDiary)
		diaries.PUT("/:id", diaryService.UpdateDiary)
		diaries.DELETE("/:id", diaryService.DeleteDiary)
	}

	// 媒体文件相关路由
	media := v1.Group("/media")
	media.Use(userService.AuthMiddleware())
	{
		media.POST("/upload", mediaService.UploadFile)
		media.GET("", mediaService.GetMediaFiles)
		media.GET("/:id", mediaService.GetMediaFile)
		media.DELETE("/:id", mediaService.DeleteMediaFile)
		media.PUT("/:id/caption", mediaService.UpdateCaption)
	}

	// 相册相关路由
	albums := v1.Group("/albums")
	albums.Use(userService.AuthMiddleware())
	{
		albums.GET("", albumService.GetAlbums)
		albums.POST("", albumService.CreateAlbum)
		albums.GET("/:id", albumService.GetAlbum)
		albums.PUT("/:id", albumService.UpdateAlbum)
		albums.DELETE("/:id", albumService.DeleteAlbum)
		albums.GET("/:id/media", albumService.GetAlbumMedia)
		albums.POST("/:id/media", albumService.AddMediaToAlbum)
		albums.DELETE("/:id/media/:media_id", albumService.RemoveMediaFromAlbum)
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})
}
