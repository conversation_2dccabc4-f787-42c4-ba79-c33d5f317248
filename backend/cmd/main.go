package main

import (
	"log"
	"net/http"

	"diary-backend/config"
	"diary-backend/internal/api"
	"diary-backend/internal/storage"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 初始化数据库
	db, err := storage.InitDB()
	if err != nil {
		log.Fatal("Failed to initialize database:", err)
	}
	defer db.Close()

	// 创建Gin路由器
	r := gin.Default()

	// 设置最大文件上传大小（设置为很大的值，实际上不限制）
	r.MaxMultipartMemory = 1024 << 20 // 1GB

	// 配置CORS
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = cfg.AllowOrigins
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	corsConfig.AllowHeaders = []string{"Origin", "Content-Type", "Authorization"}
	r.Use(cors.New(corsConfig))

	// 静态文件服务 - 用于访问上传的图片和视频
	r.Static("/uploads", cfg.UploadPath)

	// 设置API路由
	api.SetupRoutes(r, db)

	// 启动服务器
	log.Printf("Server starting on :%s", cfg.Port)
	if err := http.ListenAndServe(":"+cfg.Port, r); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
