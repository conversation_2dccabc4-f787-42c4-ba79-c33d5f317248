package config

import (
	"os"
	"strings"
)

type Config struct {
	Port         string
	AllowOrigins []string
	DatabasePath string
	UploadPath   string
}

func Load() *Config {
	config := &Config{
		Port:         getEnv("PORT", "8080"),
		DatabasePath: getEnv("DATABASE_PATH", "./diary.db"),
		UploadPath:   getEnv("UPLOAD_PATH", "./uploads"),
	}

	// 配置允许的域名
	origins := getEnv("ALLOWED_ORIGINS", "http://localhost:5173")
	config.AllowOrigins = strings.Split(origins, ",")

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
