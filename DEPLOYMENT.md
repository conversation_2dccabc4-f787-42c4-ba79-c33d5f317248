# 情侣日记应用部署指南

## 📋 部署前准备

### 1. 服务器要求
- Linux 服务器（推荐 Ubuntu 20.04+）
- 至少 1GB 内存
- 至少 10GB 存储空间
- 已安装 Node.js 16+ 和 Go 1.19+

### 2. 域名准备
- 准备一个域名（如：your-domain.com）
- 配置 DNS 解析到服务器 IP

## 🔧 配置修改

### 1. 修改前端生产环境配置
编辑 `frontend/.env.production` 文件：
```bash
# 将 your-domain.com 替换为您的实际域名
VITE_API_BASE_URL=https://your-domain.com/api/v1
VITE_UPLOAD_BASE_URL=https://your-domain.com
```

### 2. 修改后端生产环境配置
编辑 `.env.production` 文件：
```bash
PORT=8080
# 将 your-domain.com 替换为您的实际域名
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com
DATABASE_PATH=./diary.db
UPLOAD_PATH=./uploads
```

## 🚀 构建和打包

### 1. 运行构建脚本
```bash
./build.sh
```

### 2. 检查构建结果
构建完成后，`dist/` 目录包含：
- `diary-server` - 后端可执行文件
- `index.html` 和静态资源 - 前端文件
- `.env` - 生产环境配置
- `start.sh` - 启动脚本
- `uploads/` - 上传文件目录

## 📤 上传到服务器

### 方法1：使用 scp
```bash
# 将整个 dist 目录上传到服务器
scp -r dist/ user@your-server-ip:/path/to/app/

# 或者打包后上传
tar -czf diary-app.tar.gz dist/
scp diary-app.tar.gz user@your-server-ip:/path/to/app/
```

### 方法2：使用 rsync
```bash
rsync -avz dist/ user@your-server-ip:/path/to/app/
```

## 🔧 服务器配置

### 1. 解压和设置权限（如果使用tar包）
```bash
cd /path/to/app
tar -xzf diary-app.tar.gz
chmod +x diary-server start.sh
```

### 2. 安装和配置 Nginx（推荐）
```bash
sudo apt update
sudo apt install nginx

# 创建 Nginx 配置文件
sudo nano /etc/nginx/sites-available/diary
```

Nginx 配置内容：
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 前端静态文件
    location / {
        root /path/to/app;
        try_files $uri $uri/ /index.html;
    }
    
    # API 请求代理到后端
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 上传文件访问
    location /uploads/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

启用配置：
```bash
sudo ln -s /etc/nginx/sites-available/diary /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 3. 配置 SSL（推荐使用 Let's Encrypt）
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

## 🚀 启动应用

### 方法1：直接启动
```bash
cd /path/to/app
./start.sh
```

### 方法2：使用 systemd（推荐）
```bash
# 修改 diary.service 文件中的路径
sudo nano diary.service

# 复制服务文件
sudo cp diary.service /etc/systemd/system/

# 启动服务
sudo systemctl daemon-reload
sudo systemctl enable diary
sudo systemctl start diary

# 检查状态
sudo systemctl status diary
```

## 📊 监控和维护

### 查看日志
```bash
# systemd 服务日志
sudo journalctl -u diary -f

# 或者直接查看应用日志
tail -f /path/to/app/app.log
```

### 重启服务
```bash
sudo systemctl restart diary
```

### 更新应用
1. 停止服务：`sudo systemctl stop diary`
2. 备份数据：`cp diary.db diary.db.backup`
3. 上传新版本文件
4. 启动服务：`sudo systemctl start diary`

## 🔒 安全建议

1. **防火墙配置**
```bash
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

2. **定期备份数据库**
```bash
# 创建备份脚本
cat > backup.sh << 'EOF'
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
cp /path/to/app/diary.db /path/to/backups/diary_$DATE.db
# 保留最近30天的备份
find /path/to/backups -name "diary_*.db" -mtime +30 -delete
EOF

chmod +x backup.sh

# 添加到 crontab（每天凌晨2点备份）
echo "0 2 * * * /path/to/backup.sh" | crontab -
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
```bash
sudo lsof -i :8080
sudo kill -9 <PID>
```

2. **权限问题**
```bash
sudo chown -R www-data:www-data /path/to/app
sudo chmod -R 755 /path/to/app
```

3. **数据库权限**
```bash
chmod 666 diary.db
```

4. **查看详细错误**
```bash
# 直接运行查看错误信息
./diary-server
```

## 📞 技术支持

如果遇到问题，请检查：
1. 服务器日志
2. Nginx 错误日志：`sudo tail -f /var/log/nginx/error.log`
3. 应用日志
4. 防火墙设置
5. 域名 DNS 解析
