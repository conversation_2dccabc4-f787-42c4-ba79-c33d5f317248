import{u as G,r as c,q as F,j as H,c as J,b as r,d as s,w as o,g as d,e as p,m as x,D as K,k as O,n as Q,x as C,t as y,l as D,G as R,o as b}from"./index-ZX68bbwO.js";import{c as n}from"./message-D5rV6EqC.js";import{_ as W}from"./_plugin-vue_export-helper-DlAUqK2U.js";const X={class:"user-management"},Y={class:"header"},Z={class:"action-buttons"},ee={class:"delete-warning"},te={class:"warning-text"},ae={class:"dialog-footer"},se={__name:"UserManagement",setup(oe){const f=G(),U=c([]),m=c(!1),w=c(!1),k=c(null),h=c(null),_=c(!1),v=c(null),z=F(()=>{var t;return(t=f.user.value)==null?void 0:t.id}),$=t=>t?new Date(t).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-",B=t=>{switch(t){case"approved":return"success";case"pending":return"warning";case"rejected":return"danger";default:return"info"}},V=t=>{switch(t){case"approved":return"已通过";case"pending":return"待审批";case"rejected":return"已拒绝";default:return"未知"}},g=async()=>{m.value=!0;try{const t=f.token.value,i=await(await fetch("/api/v1/users",{headers:{Authorization:`Bearer ${t}`}})).json();i.code===200?U.value=i.data.users||[]:n.error(i.message||"获取用户列表失败")}catch(t){console.error("获取用户列表失败:",t),n.error("获取用户列表失败")}finally{m.value=!1}},T=t=>{v.value=t,_.value=!0},N=async()=>{if(v.value){w.value=!0;try{const t=f.token.value,i=await(await fetch(`/api/v1/users/${v.value.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}})).json();i.code===200?(n.success("用户删除成功"),_.value=!1,v.value=null,await g()):n.error(i.message||"删除用户失败")}catch(t){console.error("删除用户失败:",t),n.error("删除用户失败")}finally{w.value=!1}}},S=async t=>{k.value=t.id;try{const e=f.token.value,l=await(await fetch(`/api/v1/users/${t.id}/approve`,{method:"PUT",headers:{Authorization:`Bearer ${e}`}})).json();l.code===200?(n.success("用户审批成功"),await g()):n.error(l.message||"审批用户失败")}catch(e){console.error("审批用户失败:",e),n.error("审批用户失败")}finally{k.value=null}},A=async t=>{h.value=t.id;try{const e=f.token.value,l=await(await fetch(`/api/v1/users/${t.id}/reject`,{method:"PUT",headers:{Authorization:`Bearer ${e}`}})).json();l.code===200?(n.success("用户已被拒绝"),await g()):n.error(l.message||"拒绝用户失败")}catch(e){console.error("拒绝用户失败:",e),n.error("拒绝用户失败")}finally{h.value=null}},E=t=>{if(w.value){n.warning("正在删除用户，请稍候...");return}t()};return H(()=>{g()}),(t,e)=>{const i=p("el-icon"),l=p("el-button"),u=p("el-table-column"),M=p("el-tag"),I=p("el-table"),L=p("el-card"),P=p("el-dialog"),q=Q("loading");return b(),J("div",X,[r("div",Y,[e[3]||(e[3]=r("h1",null,"用户管理",-1)),s(l,{onClick:g,loading:m.value,type:"primary"},{default:o(()=>[s(i,null,{default:o(()=>[s(x(K))]),_:1}),e[2]||(e[2]=d(" 刷新 ",-1))]),_:1,__:[2]},8,["loading"])]),s(L,{class:"user-list-card"},{default:o(()=>[O((b(),C(I,{data:U.value,style:{width:"100%"},"default-sort":{prop:"created_at",order:"descending"}},{default:o(()=>[s(u,{prop:"id",label:"ID",width:"80",sortable:""}),s(u,{prop:"username",label:"用户名",width:"150",sortable:""}),s(u,{prop:"email",label:"邮箱",width:"200",sortable:""}),s(u,{prop:"status",label:"状态",width:"120",sortable:""},{default:o(a=>[s(M,{type:B(a.row.status),size:"small"},{default:o(()=>[d(y(V(a.row.status)),1)]),_:2},1032,["type"])]),_:1}),s(u,{prop:"created_at",label:"注册时间",width:"180",sortable:""},{default:o(a=>[d(y($(a.row.created_at)),1)]),_:1}),s(u,{prop:"updated_at",label:"最后更新",width:"180",sortable:""},{default:o(a=>[d(y($(a.row.updated_at)),1)]),_:1}),s(u,{label:"操作",width:"200",fixed:"right"},{default:o(a=>[r("div",Z,[a.row.status==="pending"?(b(),C(l,{key:0,type:"success",size:"small",onClick:j=>S(a.row),loading:k.value===a.row.id},{default:o(()=>e[4]||(e[4]=[d(" 通过 ",-1)])),_:2,__:[4]},1032,["onClick","loading"])):D("",!0),a.row.status==="pending"?(b(),C(l,{key:1,type:"warning",size:"small",onClick:j=>A(a.row),loading:h.value===a.row.id},{default:o(()=>e[5]||(e[5]=[d(" 拒绝 ",-1)])),_:2,__:[5]},1032,["onClick","loading"])):D("",!0),s(l,{type:"danger",size:"small",onClick:j=>T(a.row),disabled:a.row.id===z.value},{default:o(()=>e[6]||(e[6]=[d(" 删除 ",-1)])),_:2,__:[6]},1032,["onClick","disabled"])])]),_:1})]),_:1},8,["data"])),[[q,m.value]])]),_:1}),s(P,{modelValue:_.value,"onUpdate:modelValue":e[1]||(e[1]=a=>_.value=a),title:"确认删除用户",width:"400px","before-close":E},{footer:o(()=>[r("span",ae,[s(l,{onClick:e[0]||(e[0]=a=>_.value=!1)},{default:o(()=>e[11]||(e[11]=[d("取消",-1)])),_:1,__:[11]}),s(l,{type:"danger",onClick:N,loading:w.value},{default:o(()=>e[12]||(e[12]=[d(" 确认删除 ",-1)])),_:1,__:[12]},8,["loading"])])]),default:o(()=>{var a;return[r("div",ee,[s(i,{class:"warning-icon"},{default:o(()=>[s(x(R))]),_:1}),r("div",te,[e[8]||(e[8]=r("p",null,[r("strong",null,"警告：此操作不可撤销！")],-1)),r("p",null,[e[7]||(e[7]=d("您即将删除用户：",-1)),r("strong",null,y((a=v.value)==null?void 0:a.username),1)]),e[9]||(e[9]=r("p",null,"这将同时删除该用户的：",-1)),e[10]||(e[10]=r("ul",null,[r("li",null,"所有日记记录"),r("li",null,"所有上传的媒体文件"),r("li",null,"所有相关数据")],-1))])])]}),_:1},8,["modelValue"])])}}},ie=W(se,[["__scopeId","data-v-aba4d2cd"]]);export{ie as default};
