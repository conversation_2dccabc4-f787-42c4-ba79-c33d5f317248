import{r as p,j as C,c as a,b as o,k as V,l as d,d as l,w as c,g as y,e as m,m as $,s as b,n as L,F as N,p as x,o as n,t as r}from"./index-ZX68bbwO.js";import{d as B}from"./index-Co4Nq38H.js";import{c as I}from"./message-D5rV6EqC.js";import{_ as S}from"./_plugin-vue_export-helper-DlAUqK2U.js";const F={class:"diary-list"},M={class:"header-actions"},j={class:"search-bar"},z={class:"diary-grid"},A=["onClick"],E={class:"diary-header"},K={class:"diary-date"},P={class:"diary-content"},T={class:"diary-meta"},U={key:0,class:"mood"},q={key:1,class:"weather"},G={key:2,class:"location"},H={key:0,class:"empty-state"},J={__name:"DiaryList",setup(O){const u=p([]),i=p(!1),_=p(""),v=async()=>{try{i.value=!0;const s=await B.getDiaries({keyword:_.value});s.code===200&&(u.value=s.data.diaries||[])}catch(s){console.error("Load diaries error:",s),I.error("加载日记失败")}finally{i.value=!1}},f=()=>{v()},g=s=>new Date(s).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"});return C(()=>{v()}),(s,t)=>{const h=m("el-button"),k=m("el-icon"),w=m("el-input"),D=L("loading");return n(),a("div",F,[o("div",M,[t[4]||(t[4]=o("h2",null,"📖 我们的日记",-1)),l(h,{type:"primary",onClick:t[0]||(t[0]=e=>s.$router.push("/diary/new"))},{default:c(()=>t[3]||(t[3]=[y(" ✍️ 写日记 ",-1)])),_:1,__:[3]})]),o("div",j,[l(w,{modelValue:_.value,"onUpdate:modelValue":t[1]||(t[1]=e=>_.value=e),placeholder:"搜索日记...",onInput:f,clearable:""},{prefix:c(()=>[l(k,null,{default:c(()=>[l($(b))]),_:1})]),_:1},8,["modelValue"])]),V((n(),a("div",z,[(n(!0),a(N,null,x(u.value,e=>(n(),a("div",{key:e.id,class:"diary-card",onClick:Q=>s.$router.push(`/diary/${e.id}`)},[o("div",E,[o("h3",null,r(e.title),1),o("span",K,r(g(e.created_at)),1)]),o("div",P,r(e.content.substring(0,100))+r(e.content.length>100?"...":""),1),o("div",T,[e.mood?(n(),a("span",U,r(e.mood),1)):d("",!0),e.weather?(n(),a("span",q,r(e.weather),1)):d("",!0),e.location?(n(),a("span",G,"📍 "+r(e.location),1)):d("",!0)])],8,A))),128))])),[[D,i.value]]),u.value.length===0&&!i.value?(n(),a("div",H,[t[6]||(t[6]=o("p",null,"还没有日记，开始记录美好回忆吧！",-1)),l(h,{type:"primary",onClick:t[2]||(t[2]=e=>s.$router.push("/diary/new"))},{default:c(()=>t[5]||(t[5]=[y(" 写第一篇日记 ",-1)])),_:1,__:[5]})])):d("",!0)])}}},Z=S(J,[["__scopeId","data-v-a05ee9d3"]]);export{Z as default};
