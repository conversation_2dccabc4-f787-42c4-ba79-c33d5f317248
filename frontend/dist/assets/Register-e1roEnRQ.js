import{r as c,a as y,c as x,b as i,d as s,w as o,e as u,f as k,g,h as P,i as R,o as z}from"./index-ZX68bbwO.js";import{c as f}from"./message-D5rV6EqC.js";import{u as C}from"./index-Co4Nq38H.js";import{_ as U}from"./_plugin-vue_export-helper-DlAUqK2U.js";const q={class:"register-container"},h={class:"register-card"},B={class:"register-footer"},F={__name:"Register",setup(M){const _=R(),d=c(),m=c(!1),r=y({username:"",email:"",password:"",confirmPassword:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在3到20个字符",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(t,e,a)=>{e!==r.password?a(new Error("两次输入的密码不一致")):a()},trigger:"blur"}]},p=async()=>{if(d.value)try{await d.value.validate(),m.value=!0;const t=await C.register({username:r.username,email:r.email,password:r.password});t.code===201?(f.success("注册成功，请登录"),_.push("/login")):f.error(t.message||"注册失败")}catch(t){console.error("Register error:",t),f.error("注册失败，请检查网络连接")}finally{m.value=!1}};return(t,e)=>{const a=u("el-input"),n=u("el-form-item"),v=u("el-button"),V=u("el-form"),b=u("router-link");return z(),x("div",q,[i("div",h,[e[7]||(e[7]=i("div",{class:"register-header"},[i("h2",null,"💕 加入我们"),i("p",null,"创建你们的专属回忆空间")],-1)),s(V,{ref_key:"registerFormRef",ref:d,model:r,rules:w,class:"register-form",onSubmit:P(p,["prevent"])},{default:o(()=>[s(n,{prop:"username"},{default:o(()=>[s(a,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=l=>r.username=l),placeholder:"用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),s(n,{prop:"email"},{default:o(()=>[s(a,{modelValue:r.email,"onUpdate:modelValue":e[1]||(e[1]=l=>r.email=l),placeholder:"邮箱",size:"large","prefix-icon":"Message"},null,8,["modelValue"])]),_:1}),s(n,{prop:"password"},{default:o(()=>[s(a,{modelValue:r.password,"onUpdate:modelValue":e[2]||(e[2]=l=>r.password=l),type:"password",placeholder:"密码",size:"large","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),s(n,{prop:"confirmPassword"},{default:o(()=>[s(a,{modelValue:r.confirmPassword,"onUpdate:modelValue":e[3]||(e[3]=l=>r.confirmPassword=l),type:"password",placeholder:"确认密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:k(p,["enter"])},null,8,["modelValue"])]),_:1}),s(n,null,{default:o(()=>[s(v,{type:"primary",size:"large",loading:m.value,onClick:p,class:"register-button"},{default:o(()=>e[4]||(e[4]=[g(" 注册 ",-1)])),_:1,__:[4]},8,["loading"])]),_:1})]),_:1},8,["model"]),i("div",B,[i("p",null,[e[6]||(e[6]=g("已有账号？ ",-1)),s(b,{to:"/login",class:"login-link"},{default:o(()=>e[5]||(e[5]=[g("立即登录",-1)])),_:1,__:[5]})])])])])}}},A=U(F,[["__scopeId","data-v-bca2e89d"]]);export{A as default};
