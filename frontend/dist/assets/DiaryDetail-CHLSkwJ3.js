import{r as g,j as N,k as $,n as V,c as u,b as o,l as r,d as p,w as i,g as l,e as k,t as n,x as m,v as E,i as M,o as s,E as T}from"./index-ZX68bbwO.js";import{c}from"./message-D5rV6EqC.js";import{d as h}from"./index-Co4Nq38H.js";import{_ as I}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L={class:"diary-detail"},R={class:"detail-header"},S={key:0,class:"header-actions"},j={key:0,class:"diary-content"},z={class:"diary-header"},A={class:"diary-title"},P={class:"diary-meta"},q={class:"diary-date"},F={key:0,class:"updated-date"},G={key:0,class:"diary-tags"},H={class:"diary-body"},J={class:"content-text"},K={key:1,class:"empty-state"},O={__name:"DiaryDetail",setup(Q){const _=M(),D=E(),d=g(!1),e=g({}),w=async()=>{try{d.value=!0;const a=await h.getDiary(D.params.id);a.code===200?e.value=a.data:(c.error("日记不存在"),_.push("/diaries"))}catch(a){console.error("Load diary error:",a),c.error("加载日记失败"),_.push("/diaries")}finally{d.value=!1}},f=a=>new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),x=()=>{_.push(`/diary/${e.value.id}/edit`)},C=async()=>{try{await T.confirm("确定要删除这篇日记吗？删除后无法恢复。","确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"}),d.value=!0;const a=await h.deleteDiary(e.value.id);a.code===200?(c.success("删除成功"),_.push("/diaries")):c.error(a.message||"删除失败")}catch(a){a!=="cancel"&&(console.error("Delete diary error:",a),c.error("删除失败"))}finally{d.value=!1}};return N(()=>{w()}),(a,t)=>{const y=k("el-button"),v=k("el-tag"),b=V("loading");return $((s(),u("div",L,[o("div",R,[p(y,{onClick:t[0]||(t[0]=B=>a.$router.back()),type:"text",class:"back-btn"},{default:i(()=>t[2]||(t[2]=[l(" ← 返回 ",-1)])),_:1,__:[2]}),e.value.id?(s(),u("div",S,[p(y,{onClick:x,type:"primary",plain:""},{default:i(()=>t[3]||(t[3]=[l(" ✏️ 编辑 ",-1)])),_:1,__:[3]}),p(y,{onClick:C,type:"danger",plain:""},{default:i(()=>t[4]||(t[4]=[l(" 🗑️ 删除 ",-1)])),_:1,__:[4]})])):r("",!0)]),e.value.id?(s(),u("div",j,[o("div",z,[o("h1",A,n(e.value.title),1),o("div",P,[o("span",q,n(f(e.value.created_at)),1),e.value.updated_at!==e.value.created_at?(s(),u("span",F," (已编辑于 "+n(f(e.value.updated_at))+") ",1)):r("",!0)])]),e.value.mood||e.value.weather||e.value.location?(s(),u("div",G,[e.value.mood?(s(),m(v,{key:0,type:"success",class:"tag"},{default:i(()=>[l(n(e.value.mood),1)]),_:1})):r("",!0),e.value.weather?(s(),m(v,{key:1,type:"info",class:"tag"},{default:i(()=>[l(n(e.value.weather),1)]),_:1})):r("",!0),e.value.location?(s(),m(v,{key:2,type:"warning",class:"tag"},{default:i(()=>[l(" 📍 "+n(e.value.location),1)]),_:1})):r("",!0),e.value.is_private?(s(),m(v,{key:3,type:"danger",class:"tag"},{default:i(()=>t[5]||(t[5]=[l(" 🔒 私密 ",-1)])),_:1,__:[5]})):r("",!0)])):r("",!0),o("div",H,[o("div",J,n(e.value.content),1)])])):d.value?r("",!0):(s(),u("div",K,[t[7]||(t[7]=o("p",null,"日记不存在或已被删除",-1)),p(y,{onClick:t[1]||(t[1]=B=>a.$router.push("/diaries")),type:"primary"},{default:i(()=>t[6]||(t[6]=[l(" 返回日记列表 ",-1)])),_:1,__:[6]})]))])),[[b,d.value]])}}},Z=I(O,[["__scopeId","data-v-5a109d07"]]);export{Z as default};
