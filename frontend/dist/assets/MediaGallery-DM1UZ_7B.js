import{r as d,q as O,y as R,j as J,c,b as a,k as K,l as Q,d as s,w as l,e as v,g as i,n as X,F as Y,p as Z,m as y,t as g,E as ee,o as p,z as te,h as P}from"./index-ZX68bbwO.js";import{c as r}from"./message-D5rV6EqC.js";import{m as D}from"./index-Co4Nq38H.js";import{_ as ae}from"./_plugin-vue_export-helper-DlAUqK2U.js";const oe={class:"media-gallery"},se={class:"gallery-header"},le={class:"header-actions"},ie={class:"filter-bar"},ne={class:"media-grid"},de=["onClick"],re={class:"media-preview"},ue=["src","alt"],ce={key:1,class:"video-preview"},pe=["src"],ve={class:"video-overlay"},_e={class:"media-info"},me={class:"media-caption"},fe={class:"media-date"},ye={class:"media-actions"},ge={key:0,class:"empty-state"},we={class:"media-preview-container"},ke={class:"media-viewer"},Ce=["src","alt"],be=["src"],Ve={class:"media-info-panel"},$e={class:"info-item"},xe={class:"value"},Me={class:"info-item"},De={class:"value"},Ue={class:"info-item"},Be={class:"value"},he={class:"preview-controls"},Fe={__name:"MediaGallery",setup(ze){const w="https://your-domain.com",k=d(!1),b=d(!1),V=d([]),$=d(""),_=d(!1),m=d(!1),n=d({}),C=d(""),U=d(null),x=d(null),B=O(()=>({Authorization:`Bearer ${localStorage.getItem("token")}`})),f=async()=>{try{k.value=!0;const t=await D.getMediaFiles({type:$.value});t.code===200&&(V.value=t.data.files||[])}catch(t){console.error("Load media files error:",t),r.error("加载文件失败")}finally{k.value=!1}},h=t=>t.type.startsWith("image/")||t.type.startsWith("video/")?!0:(r.error("只能上传图片或视频文件"),!1),F=t=>{t.code===201?(r.success("上传成功"),f()):r.error(t.message||"上传失败")},z=()=>{ElMessage.error("上传失败，请检查网络连接")},I=t=>{n.value=t,_.value=!0};R(_,t=>{!t&&x.value&&x.value.pause()});const E=t=>{U.value=t,C.value=t.caption||"",m.value=!0},G=async()=>{try{b.value=!0;const t=await D.updateCaption(U.value.id,{caption:C.value});t.code===200?(r.success("更新成功"),m.value=!1,f()):r.error(t.message||"更新失败")}catch(t){console.error("Update caption error:",t),r.error("更新失败")}finally{b.value=!1}},T=async t=>{try{await ee.confirm("确定要删除这个文件吗？删除后无法恢复。","确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning",confirmButtonClass:"el-button--danger"});const e=await D.deleteMediaFile(t.id);e.code===200?(r.success("删除成功"),f()):r.error(e.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("Delete media error:",e),r.error("删除失败"))}},L=t=>new Date(t).toLocaleDateString("zh-CN");return J(()=>{f()}),(t,e)=>{const u=v("el-button"),N=v("el-upload"),M=v("el-radio-button"),W=v("el-radio-group"),j=v("el-icon"),S=v("el-dialog"),q=v("el-input"),H=X("loading");return p(),c("div",oe,[a("div",se,[e[9]||(e[9]=a("h2",null,"📸 我们的相册",-1)),a("div",le,[s(N,{action:"/api/v1/media/upload",headers:B.value,"on-success":F,"on-error":z,"before-upload":h,"show-file-list":!1,accept:"image/*,video/*",class:"upload-btn"},{default:l(()=>[s(u,{type:"primary"},{default:l(()=>e[8]||(e[8]=[i(" 📤 上传文件 ",-1)])),_:1,__:[8]})]),_:1},8,["headers"])])]),a("div",ie,[s(W,{modelValue:$.value,"onUpdate:modelValue":e[0]||(e[0]=o=>$.value=o),onChange:f},{default:l(()=>[s(M,{label:""},{default:l(()=>e[10]||(e[10]=[i("全部",-1)])),_:1,__:[10]}),s(M,{label:"image"},{default:l(()=>e[11]||(e[11]=[i("📷 照片",-1)])),_:1,__:[11]}),s(M,{label:"video"},{default:l(()=>e[12]||(e[12]=[i("🎬 视频",-1)])),_:1,__:[12]})]),_:1},8,["modelValue"])]),K((p(),c("div",ne,[(p(!0),c(Y,null,Z(V.value,o=>(p(),c("div",{key:o.id,class:"media-item",onClick:A=>I(o)},[a("div",re,[o.file_type==="image"?(p(),c("img",{key:0,src:`${y(w)}/uploads/${o.file_name}`,alt:o.caption||"照片",class:"media-image"},null,8,ue)):(p(),c("div",ce,[a("video",{src:`${y(w)}/uploads/${o.file_name}`,class:"media-video",preload:"metadata"},null,8,pe),a("div",ve,[s(j,{class:"play-icon"},{default:l(()=>[s(y(te))]),_:1})])]))]),a("div",_e,[a("p",me,g(o.caption||"无描述"),1),a("p",fe,g(L(o.created_at)),1)]),a("div",ye,[s(u,{onClick:P(A=>E(o),["stop"]),type:"text",size:"small"},{default:l(()=>e[13]||(e[13]=[i(" 编辑 ",-1)])),_:2,__:[13]},1032,["onClick"]),s(u,{onClick:P(A=>T(o),["stop"]),type:"text",size:"small",class:"delete-btn"},{default:l(()=>e[14]||(e[14]=[i(" 删除 ",-1)])),_:2,__:[14]},1032,["onClick"])])],8,de))),128))])),[[H,k.value]]),V.value.length===0&&!k.value?(p(),c("div",ge,[e[16]||(e[16]=a("p",null,"还没有上传任何文件",-1)),s(N,{action:"/api/v1/media/upload",headers:B.value,"on-success":F,"on-error":z,"before-upload":h,"show-file-list":!1,accept:"image/*,video/*"},{default:l(()=>[s(u,{type:"primary",size:"large"},{default:l(()=>e[15]||(e[15]=[i(" 上传第一个文件 ",-1)])),_:1,__:[15]})]),_:1},8,["headers"])])):Q("",!0),s(S,{modelValue:_.value,"onUpdate:modelValue":e[4]||(e[4]=o=>_.value=o),title:n.value.caption||"媒体文件",width:"90%",center:"",class:"media-preview-dialog"},{footer:l(()=>[a("div",he,[s(u,{onClick:e[1]||(e[1]=o=>_.value=!1)},{default:l(()=>e[20]||(e[20]=[i("关闭",-1)])),_:1,__:[20]}),s(u,{type:"primary",onClick:e[2]||(e[2]=o=>E(n.value))},{default:l(()=>e[21]||(e[21]=[i(" 编辑描述 ",-1)])),_:1,__:[21]}),s(u,{type:"danger",onClick:e[3]||(e[3]=o=>T(n.value))},{default:l(()=>e[22]||(e[22]=[i(" 删除文件 ",-1)])),_:1,__:[22]})])]),default:l(()=>[a("div",we,[a("div",ke,[n.value.file_type==="image"?(p(),c("img",{key:0,src:`${y(w)}/uploads/${n.value.file_name}`,class:"preview-image",alt:n.value.caption},null,8,Ce)):(p(),c("video",{key:1,ref_key:"videoPlayer",ref:x,src:`${y(w)}/uploads/${n.value.file_name}`,controls:"",autoplay:"",class:"preview-video"},null,8,be))]),a("div",Ve,[a("div",$e,[e[17]||(e[17]=a("span",{class:"label"},"描述：",-1)),a("span",xe,g(n.value.caption||"无描述"),1)]),a("div",Me,[e[18]||(e[18]=a("span",{class:"label"},"类型：",-1)),a("span",De,g(n.value.file_type==="image"?"图片":"视频"),1)]),a("div",Ue,[e[19]||(e[19]=a("span",{class:"label"},"上传时间：",-1)),a("span",Be,g(L(n.value.created_at)),1)])])])]),_:1},8,["modelValue","title"]),s(S,{modelValue:m.value,"onUpdate:modelValue":e[7]||(e[7]=o=>m.value=o),title:"编辑描述",width:"400px",center:""},{footer:l(()=>[s(u,{onClick:e[6]||(e[6]=o=>m.value=!1)},{default:l(()=>e[23]||(e[23]=[i("取消",-1)])),_:1,__:[23]}),s(u,{type:"primary",onClick:G,loading:b.value},{default:l(()=>e[24]||(e[24]=[i(" 保存 ",-1)])),_:1,__:[24]},8,["loading"])]),default:l(()=>[s(q,{modelValue:C.value,"onUpdate:modelValue":e[5]||(e[5]=o=>C.value=o),type:"textarea",rows:3,placeholder:"为这个文件添加描述..."},null,8,["modelValue"])]),_:1},8,["modelValue"])])}}},Se=ae(Fe,[["__scopeId","data-v-facfed8d"]]);export{Se as default};
