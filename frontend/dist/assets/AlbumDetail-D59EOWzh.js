import{r as n,q as ne,v as de,j as re,y as ce,c as o,b as t,k as H,l as g,x as J,d as u,w as r,g as y,e as V,t as _,B as K,n as ue,F as Q,p as W,m as h,A as m,E as X,i as ve,o as s,C as pe}from"./index-ZX68bbwO.js";import{a as w}from"./index-Co4Nq38H.js";import{_ as _e}from"./_plugin-vue_export-helper-DlAUqK2U.js";const me={class:"album-detail"},fe={class:"header"},ye={class:"header-left"},ge={key:0,class:"album-info"},he={class:"description"},ke={class:"meta"},be={key:0},we={key:1},$e={key:2},Ce={class:"media-count"},Me={class:"header-actions"},Ae={class:"media-grid"},De={class:"media-preview"},Ve=["src","alt","onClick"],xe=["src","onClick"],Be={class:"media-info"},Le={class:"caption"},Re={class:"media-meta"},ze={class:"add-date"},Pe={key:0,class:"empty-state"},Se={class:"add-media-content"},Ee={class:"available-media"},Ne={class:"media-selection-grid"},Fe=["onClick"],Ue={class:"media-preview-small"},Ie=["src","alt"],Oe=["src"],je={class:"selection-overlay"},qe={class:"media-preview-container"},Te={class:"media-viewer"},Ge=["src","alt"],He=["src"],Je={class:"media-info-panel"},Ke={class:"info-item"},Qe={class:"value"},We={class:"info-item"},Xe={class:"value"},Ye={class:"info-item"},Ze={class:"value"},ea={class:"preview-controls"},aa={__name:"AlbumDetail",setup(ta){const k="https://your-domain.com",Y=de();ve();const v=n(null),x=n([]),E=n([]),A=n(!1),B=n(!1),L=n(!1),f=n(!1),c=n([]),R=n(1),N=n(20),z=n(0),$=n(!1),d=n(null),Z=n(null),P=n(!1),C=ne(()=>Y.params.id),D=async()=>{try{const l=await w.get(`/albums/${C.value}`);v.value=l.data}catch(l){m.error("加载相册信息失败"),console.error("Load album error:",l)}},M=async()=>{var l,e;A.value=!0;try{const i={page:R.value,limit:N.value},p=await w.get(`/albums/${C.value}/media`,{params:i});x.value=((l=p.data)==null?void 0:l.media_files)||[],z.value=((e=p.data)==null?void 0:e.total)||0}catch(i){m.error("加载相册媒体失败"),console.error("Load album media error:",i)}finally{A.value=!1}},ee=async()=>{var l,e,i;B.value=!0;try{const p={limit:100};((l=v.value)==null?void 0:l.type)==="photo"?p.type="image":((e=v.value)==null?void 0:e.type)==="video"&&(p.type="video");const S=await w.get("/media",{params:p});E.value=((i=S.data)==null?void 0:i.files)||[]}catch(p){m.error("加载可用媒体失败"),console.error("Load available media error:",p)}finally{B.value=!1}},ae=l=>{const e=c.value.indexOf(l);e>-1?c.value.splice(e,1):c.value.push(l)},te=async()=>{var l;if(c.value.length!==0){L.value=!0;try{const e=await w.post(`/albums/${C.value}/media`,{media_ids:c.value});m.success(`成功添加 ${((l=e.data)==null?void 0:l.added_count)||c.value.length} 个文件`),f.value=!1,c.value=[],M(),D()}catch(e){m.error("添加媒体失败"),console.error("Add media error:",e)}finally{L.value=!1}}},le=async l=>{try{await X.confirm("确定要从相册中移除这个文件吗？","确认移除",{type:"warning"}),await w.delete(`/albums/${C.value}/media/${l}`),m.success("文件移除成功"),M(),D()}catch(e){e!=="cancel"&&(m.error("移除文件失败"),console.error("Remove media error:",e))}},F=l=>{d.value=l,$.value=!0},se=async()=>{if(d.value)try{await X.confirm("确定要从相册中移除这个文件吗？","确认移除",{type:"warning"}),P.value=!0,await w.delete(`/albums/${C.value}/media/${d.value.id}`),m.success("文件移除成功"),$.value=!1,d.value=null,M(),D()}catch(l){l!=="cancel"&&(m.error("移除文件失败"),console.error("Remove media error:",l))}finally{P.value=!1}},oe=l=>{R.value=l,M()},U=l=>new Date(l).toLocaleDateString("zh-CN"),ie=()=>{f.value&&ee()};return re(()=>{D(),M()}),ce(f,ie),(l,e)=>{var j;const i=V("el-button"),p=V("el-pagination"),S=V("el-icon"),I=V("el-dialog"),O=ue("loading");return s(),o("div",me,[t("div",fe,[t("div",ye,[u(i,{onClick:e[0]||(e[0]=a=>l.$router.back()),class:"back-btn"},{default:r(()=>e[7]||(e[7]=[y(" ← 返回 ",-1)])),_:1,__:[7]}),v.value?(s(),o("div",ge,[t("h2",null,_(v.value.name),1),t("p",he,_(v.value.description||"无描述"),1),t("div",ke,[t("span",{class:K(["type-badge",v.value.type])},[v.value.type==="photo"?(s(),o("span",be,"📷 照片相册")):v.value.type==="video"?(s(),o("span",we,"🎬 视频相册")):(s(),o("span",$e,"📁 混合相册"))],2),t("span",Ce,_(v.value.media_count)+" 个文件",1)])])):g("",!0)]),t("div",Me,[u(i,{onClick:e[1]||(e[1]=a=>f.value=!0)},{default:r(()=>e[8]||(e[8]=[y(" ➕ 添加媒体 ",-1)])),_:1,__:[8]})])]),H((s(),o("div",Ae,[(s(!0),o(Q,null,W(x.value,a=>(s(),o("div",{key:a.id,class:"media-item"},[t("div",De,[a.file_type==="image"?(s(),o("img",{key:0,src:`${h(k)}/${a.file_path}`,alt:a.caption,onClick:b=>F(a),class:"clickable-media"},null,8,Ve)):a.file_type==="video"?(s(),o("video",{key:1,src:`${h(k)}/${a.file_path}`,onClick:b=>F(a),preload:"metadata",class:"clickable-media"}," 您的浏览器不支持视频播放 ",8,xe)):g("",!0)]),t("div",Be,[t("p",Le,_(a.caption||"无描述"),1),t("div",Re,[t("span",ze,_(U(a.added_at)),1),u(i,{size:"small",type:"danger",onClick:b=>le(a.id),class:"remove-btn"},{default:r(()=>e[9]||(e[9]=[y(" 移除 ",-1)])),_:2,__:[9]},1032,["onClick"])])])]))),128))])),[[O,A.value]]),!A.value&&x.value.length===0?(s(),o("div",Pe,[e[11]||(e[11]=t("div",{class:"empty-icon"},"📁",-1)),e[12]||(e[12]=t("p",null,"相册中还没有媒体文件",-1)),u(i,{type:"primary",onClick:e[2]||(e[2]=a=>f.value=!0)},{default:r(()=>e[10]||(e[10]=[y(" 添加第一个文件 ",-1)])),_:1,__:[10]})])):g("",!0),z.value>0?(s(),J(p,{key:1,"current-page":R.value,"page-size":N.value,total:z.value,layout:"prev, pager, next, total",onCurrentChange:oe,class:"pagination"},null,8,["current-page","page-size","total"])):g("",!0),u(I,{title:"添加媒体到相册",modelValue:f.value,"onUpdate:modelValue":e[4]||(e[4]=a=>f.value=a),width:"800px"},{footer:r(()=>[u(i,{onClick:e[3]||(e[3]=a=>f.value=!1)},{default:r(()=>e[14]||(e[14]=[y("取消",-1)])),_:1,__:[14]}),u(i,{type:"primary",onClick:te,loading:L.value,disabled:c.value.length===0},{default:r(()=>[y(" 添加选中的文件 ("+_(c.value.length)+") ",1)]),_:1},8,["loading","disabled"])]),default:r(()=>[t("div",Se,[H((s(),o("div",Ee,[e[13]||(e[13]=t("h4",null,"选择要添加的媒体文件：",-1)),t("div",Ne,[(s(!0),o(Q,null,W(E.value,a=>(s(),o("div",{key:a.id,class:K(["selectable-media",{selected:c.value.includes(a.id)}]),onClick:b=>ae(a.id)},[t("div",Ue,[a.file_type==="image"?(s(),o("img",{key:0,src:`${h(k)}/${a.file_path}`,alt:a.caption},null,8,Ie)):a.file_type==="video"?(s(),o("video",{key:1,src:`${h(k)}/${a.file_path}`,preload:"metadata"},null,8,Oe)):g("",!0)]),t("div",je,[c.value.includes(a.id)?(s(),J(S,{key:0},{default:r(()=>[u(h(pe))]),_:1})):g("",!0)])],10,Fe))),128))])])),[[O,B.value]])])]),_:1},8,["modelValue"]),u(I,{modelValue:$.value,"onUpdate:modelValue":e[6]||(e[6]=a=>$.value=a),title:((j=d.value)==null?void 0:j.caption)||"媒体预览",width:"90%",center:"",class:"media-preview-dialog"},{footer:r(()=>[t("div",ea,[u(i,{onClick:e[5]||(e[5]=a=>$.value=!1)},{default:r(()=>e[18]||(e[18]=[y("关闭",-1)])),_:1,__:[18]}),u(i,{type:"danger",onClick:se,loading:P.value},{default:r(()=>e[19]||(e[19]=[y(" 从相册中移除 ",-1)])),_:1,__:[19]},8,["loading"])])]),default:r(()=>{var a,b,q,T,G;return[t("div",qe,[t("div",Te,[((a=d.value)==null?void 0:a.file_type)==="image"?(s(),o("img",{key:0,src:`${h(k)}/${d.value.file_path}`,alt:d.value.caption,class:"preview-image"},null,8,Ge)):((b=d.value)==null?void 0:b.file_type)==="video"?(s(),o("video",{key:1,src:`${h(k)}/${d.value.file_path}`,controls:"",autoplay:"",class:"preview-video",ref_key:"previewVideoRef",ref:Z}," 您的浏览器不支持视频播放 ",8,He)):g("",!0)]),t("div",Je,[t("div",Ke,[e[15]||(e[15]=t("span",{class:"label"},"描述：",-1)),t("span",Qe,_(((q=d.value)==null?void 0:q.caption)||"无描述"),1)]),t("div",We,[e[16]||(e[16]=t("span",{class:"label"},"类型：",-1)),t("span",Xe,_(((T=d.value)==null?void 0:T.file_type)==="image"?"图片":"视频"),1)]),t("div",Ye,[e[17]||(e[17]=t("span",{class:"label"},"添加时间：",-1)),t("span",Ze,_(U((G=d.value)==null?void 0:G.added_at)),1)])])])]}),_:1},8,["modelValue","title"])])}}},ia=_e(aa,[["__scopeId","data-v-c7a19058"]]);export{ia as default};
