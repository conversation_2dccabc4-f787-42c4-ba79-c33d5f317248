import{u as k,r as f,a as x,c as L,b as t,d as o,w as l,e as n,f as h,g as p,h as R,i as U,o as z}from"./index-ZX68bbwO.js";import{c}from"./message-D5rV6EqC.js";import{u as B}from"./index-Co4Nq38H.js";import{_ as C}from"./_plugin-vue_export-helper-DlAUqK2U.js";const F={class:"login-container"},N={class:"login-card"},S={class:"login-footer"},q={__name:"Login",setup(I){const _=U(),v=k(),a=f(),i=f(!1),r=x({username:"",password:""}),w={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}]},u=async()=>{if(a.value)try{await a.value.validate(),i.value=!0;const s=await B.login(r);s.code===200?(v.login(s.data),c.success("登录成功"),_.push("/diaries")):c.error(s.message||"登录失败")}catch(s){console.error("Login error:",s),c.error("登录失败，请检查网络连接")}finally{i.value=!1}};return(s,e)=>{const g=n("el-input"),d=n("el-form-item"),y=n("el-button"),V=n("el-form"),b=n("router-link");return z(),L("div",F,[t("div",N,[e[5]||(e[5]=t("div",{class:"login-header"},[t("h2",null,"💕 欢迎回来"),t("p",null,"登录到我们的回忆空间")],-1)),o(V,{ref_key:"loginFormRef",ref:a,model:r,rules:w,class:"login-form",onSubmit:R(u,["prevent"])},{default:l(()=>[o(d,{prop:"username"},{default:l(()=>[o(g,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=m=>r.username=m),placeholder:"用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),o(d,{prop:"password"},{default:l(()=>[o(g,{modelValue:r.password,"onUpdate:modelValue":e[1]||(e[1]=m=>r.password=m),type:"password",placeholder:"密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:h(u,["enter"])},null,8,["modelValue"])]),_:1}),o(d,null,{default:l(()=>[o(y,{type:"primary",size:"large",loading:i.value,onClick:u,class:"login-button"},{default:l(()=>e[2]||(e[2]=[p(" 登录 ",-1)])),_:1,__:[2]},8,["loading"])]),_:1})]),_:1},8,["model"]),t("div",S,[t("p",null,[e[4]||(e[4]=p("还没有账号？ ",-1)),o(b,{to:"/register",class:"register-link"},{default:l(()=>e[3]||(e[3]=[p("立即注册",-1)])),_:1,__:[3]})])])])])}}},P=C(q,[["__scopeId","data-v-87f47a82"]]);export{P as default};
