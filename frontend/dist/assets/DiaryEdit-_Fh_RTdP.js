import{r as w,q as z,v as E,a as $,j as h,c as q,b as m,d as e,t as k,w as s,g as p,e as i,i as B,o as F}from"./index-ZX68bbwO.js";import{c}from"./message-D5rV6EqC.js";import{d as g}from"./index-Co4Nq38H.js";import{_ as N}from"./_plugin-vue_export-helper-DlAUqK2U.js";const S={class:"diary-edit"},j={class:"edit-header"},I={class:"edit-form-container"},L={class:"form-row"},M={class:"form-actions"},A={__name:"DiaryEdit",setup(O){const f=B(),_=E(),v=w(),d=w(!1),u=z(()=>!!_.params.id),a=$({title:"",content:"",mood:"",weather:"",location:"",is_private:!1}),x={title:[{required:!0,message:"请输入标题",trigger:"blur"},{max:100,message:"标题不能超过100个字符",trigger:"blur"}],content:[{required:!0,message:"请输入内容",trigger:"blur"},{max:5e3,message:"内容不能超过5000个字符",trigger:"blur"}]},D=async()=>{if(u.value)try{d.value=!0;const o=await g.getDiary(_.params.id);if(o.code===200){const l=o.data;Object.assign(a,{title:l.title,content:l.content,mood:l.mood,weather:l.weather,location:l.location,is_private:l.is_private})}else c.error("加载日记失败"),f.back()}catch(o){console.error("Load diary error:",o),c.error("加载日记失败"),f.back()}finally{d.value=!1}},U=async()=>{if(v.value)try{await v.value.validate(),d.value=!0;let o;u.value?o=await g.updateDiary(_.params.id,a):o=await g.createDiary(a),o.code===200||o.code===201?(c.success(u.value?"更新成功":"保存成功"),f.push("/diaries")):c.error(o.message||"保存失败")}catch(o){console.error("Save diary error:",o),c.error("保存失败，请检查网络连接")}finally{d.value=!1}};return h(()=>{D()}),(o,l)=>{const b=i("el-button"),y=i("el-input"),n=i("el-form-item"),t=i("el-option"),V=i("el-select"),C=i("el-checkbox"),R=i("el-form");return F(),q("div",S,[m("div",j,[m("h2",null,k(u.value?"✏️ 编辑日记":"✍️ 写日记"),1),e(b,{onClick:l[0]||(l[0]=r=>o.$router.back()),type:"text"},{default:s(()=>l[8]||(l[8]=[p("返回",-1)])),_:1,__:[8]})]),m("div",I,[e(R,{ref_key:"diaryFormRef",ref:v,model:a,rules:x,class:"diary-form","label-position":"top"},{default:s(()=>[e(n,{label:"标题",prop:"title"},{default:s(()=>[e(y,{modelValue:a.title,"onUpdate:modelValue":l[1]||(l[1]=r=>a.title=r),placeholder:"给这篇日记起个标题...",size:"large"},null,8,["modelValue"])]),_:1}),e(n,{label:"内容",prop:"content"},{default:s(()=>[e(y,{modelValue:a.content,"onUpdate:modelValue":l[2]||(l[2]=r=>a.content=r),type:"textarea",rows:10,placeholder:"记录今天的美好回忆...",resize:"none"},null,8,["modelValue"])]),_:1}),m("div",L,[e(n,{label:"心情",class:"form-item"},{default:s(()=>[e(V,{modelValue:a.mood,"onUpdate:modelValue":l[3]||(l[3]=r=>a.mood=r),placeholder:"选择心情",clearable:""},{default:s(()=>[e(t,{label:"😊 开心",value:"开心"}),e(t,{label:"😍 甜蜜",value:"甜蜜"}),e(t,{label:"🥰 幸福",value:"幸福"}),e(t,{label:"😌 平静",value:"平静"}),e(t,{label:"😔 难过",value:"难过"}),e(t,{label:"😤 生气",value:"生气"}),e(t,{label:"😴 疲惫",value:"疲惫"}),e(t,{label:"🤔 思考",value:"思考"})]),_:1},8,["modelValue"])]),_:1}),e(n,{label:"天气",class:"form-item"},{default:s(()=>[e(V,{modelValue:a.weather,"onUpdate:modelValue":l[4]||(l[4]=r=>a.weather=r),placeholder:"选择天气",clearable:""},{default:s(()=>[e(t,{label:"☀️ 晴天",value:"晴天"}),e(t,{label:"⛅ 多云",value:"多云"}),e(t,{label:"🌧️ 雨天",value:"雨天"}),e(t,{label:"❄️ 雪天",value:"雪天"}),e(t,{label:"🌫️ 雾天",value:"雾天"}),e(t,{label:"⛈️ 雷雨",value:"雷雨"})]),_:1},8,["modelValue"])]),_:1})]),e(n,{label:"地点"},{default:s(()=>[e(y,{modelValue:a.location,"onUpdate:modelValue":l[5]||(l[5]=r=>a.location=r),placeholder:"在哪里呢？","prefix-icon":"Location"},null,8,["modelValue"])]),_:1}),e(n,null,{default:s(()=>[e(C,{modelValue:a.is_private,"onUpdate:modelValue":l[6]||(l[6]=r=>a.is_private=r)},{default:s(()=>l[9]||(l[9]=[p(" 🔒 设为私密日记 ",-1)])),_:1,__:[9]},8,["modelValue"])]),_:1}),m("div",M,[e(b,{onClick:l[7]||(l[7]=r=>o.$router.back()),size:"large"},{default:s(()=>l[10]||(l[10]=[p(" 取消 ",-1)])),_:1,__:[10]}),e(b,{type:"primary",onClick:U,loading:d.value,size:"large"},{default:s(()=>[p(k(u.value?"更新":"保存"),1)]),_:1},8,["loading"])])]),_:1},8,["model"])])])}}},J=N(A,[["__scopeId","data-v-74017aaa"]]);export{J as default};
