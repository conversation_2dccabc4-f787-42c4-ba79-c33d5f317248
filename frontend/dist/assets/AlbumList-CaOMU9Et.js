import{r,a as K,j as Q,c as p,b as i,d as l,k as W,x as X,l as Y,w as a,g as m,e as u,n as Z,F as ee,p as te,t as b,A as f,i as le,o as d,m as oe,h as ae,E as se}from"./index-ZX68bbwO.js";import{a as C}from"./index-Co4Nq38H.js";import{_ as ne}from"./_plugin-vue_export-helper-DlAUqK2U.js";const re={class:"album-list"},ie={class:"header"},ue={class:"album-grid"},de=["onClick"],pe={class:"album-cover"},me=["src","alt"],ce={key:1,class:"default-cover"},_e={key:0},ve={key:1},fe={key:2},ge={class:"album-info"},ye={class:"description"},be={class:"album-meta"},ke={class:"media-count"},Ce={class:"create-date"},Ve={__name:"AlbumList",setup(we){const z="https://your-domain.com",B=le(),D=r([]),V=r(!1),w=r(!1),v=r(!1),c=r(null),k=r(""),x=r(1),F=r(12),A=r(0),s=K({name:"",description:"",type:"mixed"}),E={name:[{required:!0,message:"请输入相册名称",trigger:"blur"},{min:1,max:50,message:"相册名称长度在 1 到 50 个字符",trigger:"blur"}],type:[{required:!0,message:"请选择相册类型",trigger:"change"}]},g=r(),y=async()=>{var o,e;V.value=!0;try{const n={page:x.value,limit:F.value};k.value&&(n.type=k.value);const _=await C.get("/albums",{params:n});D.value=((o=_.data)==null?void 0:o.albums)||[],A.value=((e=_.data)==null?void 0:e.total)||0}catch(n){f.error("加载相册列表失败"),console.error("Load albums error:",n)}finally{V.value=!1}},N=o=>{B.push(`/albums/${o}`)},R=o=>{c.value=o,s.name=o.name,s.description=o.description,s.type=o.type,v.value=!0},S=async()=>{if(g.value)try{await g.value.validate(),w.value=!0,c.value?(await C.put(`/albums/${c.value.id}`,s),f.success("相册更新成功")):(await C.post("/albums",s),f.success("相册创建成功")),v.value=!1,P(),y()}catch(o){f.error(c.value?"更新相册失败":"创建相册失败"),console.error("Save album error:",o)}finally{w.value=!1}},M=async o=>{try{await se.confirm("确定要删除这个相册吗？","确认删除",{type:"warning"}),await C.delete(`/albums/${o}`),f.success("相册删除成功"),y()}catch(e){e!=="cancel"&&(f.error("删除相册失败"),console.error("Delete album error:",e))}},P=()=>{c.value=null,s.name="",s.description="",s.type="mixed",g.value&&g.value.resetFields()},q=o=>{x.value=o,y()},T=o=>new Date(o).toLocaleDateString("zh-CN");return Q(()=>{y()}),(o,e)=>{const n=u("el-button"),_=u("el-radio-button"),j=u("el-radio-group"),I=u("el-pagination"),L=u("el-input"),$=u("el-form-item"),h=u("el-option"),O=u("el-select"),G=u("el-form"),H=u("el-dialog"),J=Z("loading");return d(),p("div",re,[i("div",ie,[e[9]||(e[9]=i("h2",null,"📚 相册管理",-1)),l(n,{type:"primary",onClick:e[0]||(e[0]=t=>v.value=!0)},{default:a(()=>e[8]||(e[8]=[m(" ➕ 创建相册 ",-1)])),_:1,__:[8]})]),l(j,{modelValue:k.value,"onUpdate:modelValue":e[1]||(e[1]=t=>k.value=t),onChange:y,class:"type-filter"},{default:a(()=>[l(_,{label:""},{default:a(()=>e[10]||(e[10]=[m("全部",-1)])),_:1,__:[10]}),l(_,{label:"photo"},{default:a(()=>e[11]||(e[11]=[m("📷 照片相册",-1)])),_:1,__:[11]}),l(_,{label:"video"},{default:a(()=>e[12]||(e[12]=[m("🎬 视频相册",-1)])),_:1,__:[12]}),l(_,{label:"mixed"},{default:a(()=>e[13]||(e[13]=[m("📁 混合相册",-1)])),_:1,__:[13]})]),_:1},8,["modelValue"]),W((d(),p("div",ue,[(d(!0),p(ee,null,te(D.value,t=>(d(),p("div",{key:t.id,class:"album-card",onClick:U=>N(t.id)},[i("div",pe,[t.cover_image?(d(),p("img",{key:0,src:`${oe(z)}/${t.cover_image}`,alt:t.name},null,8,me)):(d(),p("div",ce,[t.type==="photo"?(d(),p("span",_e,"📷")):t.type==="video"?(d(),p("span",ve,"🎬")):(d(),p("span",fe,"📁"))]))]),i("div",ge,[i("h3",null,b(t.name),1),i("p",ye,b(t.description||"无描述"),1),i("div",be,[i("span",ke,b(t.media_count)+" 个文件",1),i("span",Ce,b(T(t.created_at)),1)])]),i("div",{class:"album-actions",onClick:e[2]||(e[2]=ae(()=>{},["stop"]))},[l(n,{size:"small",onClick:U=>R(t)},{default:a(()=>e[14]||(e[14]=[m("编辑",-1)])),_:2,__:[14]},1032,["onClick"]),l(n,{size:"small",type:"danger",onClick:U=>M(t.id)},{default:a(()=>e[15]||(e[15]=[m("删除",-1)])),_:2,__:[15]},1032,["onClick"])])],8,de))),128))])),[[J,V.value]]),A.value>0?(d(),X(I,{key:0,"current-page":x.value,"page-size":F.value,total:A.value,layout:"prev, pager, next, total",onCurrentChange:q,class:"pagination"},null,8,["current-page","page-size","total"])):Y("",!0),l(H,{title:c.value?"编辑相册":"创建相册",modelValue:v.value,"onUpdate:modelValue":e[7]||(e[7]=t=>v.value=t),width:"500px"},{footer:a(()=>[l(n,{onClick:e[6]||(e[6]=t=>v.value=!1)},{default:a(()=>e[16]||(e[16]=[m("取消",-1)])),_:1,__:[16]}),l(n,{type:"primary",onClick:S,loading:w.value},{default:a(()=>[m(b(c.value?"更新":"创建"),1)]),_:1},8,["loading"])]),default:a(()=>[l(G,{model:s,rules:E,ref_key:"albumFormRef",ref:g,"label-width":"80px"},{default:a(()=>[l($,{label:"相册名称",prop:"name"},{default:a(()=>[l(L,{modelValue:s.name,"onUpdate:modelValue":e[3]||(e[3]=t=>s.name=t),placeholder:"请输入相册名称"},null,8,["modelValue"])]),_:1}),l($,{label:"相册描述",prop:"description"},{default:a(()=>[l(L,{modelValue:s.description,"onUpdate:modelValue":e[4]||(e[4]=t=>s.description=t),type:"textarea",rows:3,placeholder:"请输入相册描述（可选）"},null,8,["modelValue"])]),_:1}),l($,{label:"相册类型",prop:"type"},{default:a(()=>[l(O,{modelValue:s.type,"onUpdate:modelValue":e[5]||(e[5]=t=>s.type=t),placeholder:"请选择相册类型"},{default:a(()=>[l(h,{label:"📷 照片相册",value:"photo"}),l(h,{label:"🎬 视频相册",value:"video"}),l(h,{label:"📁 混合相册",value:"mixed"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"])])}}},he=ne(Ve,[["__scopeId","data-v-336e8685"]]);export{he as default};
