import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'

const routes = [
  {
    path: '/',
    redirect: '/diaries'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue')
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue')
  },
  {
    path: '/diaries',
    name: 'Diaries',
    component: () => import('../views/DiaryList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/diary/new',
    name: 'NewDiary',
    component: () => import('../views/DiaryEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/diary/:id',
    name: 'DiaryDetail',
    component: () => import('../views/DiaryDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/diary/:id/edit',
    name: 'EditDiary',
    component: () => import('../views/DiaryEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/media',
    name: 'Media',
    component: () => import('../views/MediaGallery.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/albums',
    name: 'Albums',
    component: () => import('../views/AlbumList.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/albums/:id',
    name: 'AlbumDetail',
    component: () => import('../views/AlbumDetail.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/users',
    name: 'UserManagement',
    component: () => import('../views/UserManagement.vue'),
    meta: { requiresAuth: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()

  if (to.meta.requiresAuth && !userStore.token.value) {
    next('/login')
  } else if (to.name === 'Login' && userStore.token.value) {
    next('/diaries')
  } else {
    next()
  }
})

export default router