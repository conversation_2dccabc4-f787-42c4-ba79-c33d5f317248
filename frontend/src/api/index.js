import axios from 'axios'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  timeout: 10000
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// 用户相关API
export const userAPI = {
  login: (data) => api.post('/auth/login', data),
  register: (data) => api.post('/auth/register', data),
  getProfile: () => api.get('/auth/profile'),
  updateProfile: (data) => api.put('/auth/profile', data)
}

// 日记相关API
export const diaryAPI = {
  getDiaries: (params) => api.get('/diaries', { params }),
  getDiary: (id) => api.get(`/diaries/${id}`),
  createDiary: (data) => api.post('/diaries', data),
  updateDiary: (id, data) => api.put(`/diaries/${id}`, data),
  deleteDiary: (id) => api.delete(`/diaries/${id}`)
}

// 媒体文件相关API
export const mediaAPI = {
  uploadFile: (formData) => api.post('/media/upload', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
  getMediaFiles: (params) => api.get('/media', { params }),
  getMediaFile: (id) => api.get(`/media/${id}`),
  deleteMediaFile: (id) => api.delete(`/media/${id}`),
  updateCaption: (id, data) => api.put(`/media/${id}/caption`, data)
}

export default api