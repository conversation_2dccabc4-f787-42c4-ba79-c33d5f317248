import { ElMessage } from 'element-plus'

// 自定义消息配置，缩短显示时间
const customMessage = {
  success: (message, options = {}) => {
    return ElMessage.success({
      message,
      duration: 1500, // 1.5秒
      ...options
    })
  },
  
  error: (message, options = {}) => {
    return ElMessage.error({
      message,
      duration: 2000, // 2秒（错误消息稍长一点）
      ...options
    })
  },
  
  warning: (message, options = {}) => {
    return ElMessage.warning({
      message,
      duration: 1500, // 1.5秒
      ...options
    })
  },
  
  info: (message, options = {}) => {
    return ElMessage.info({
      message,
      duration: 1500, // 1.5秒
      ...options
    })
  }
}

export default customMessage
