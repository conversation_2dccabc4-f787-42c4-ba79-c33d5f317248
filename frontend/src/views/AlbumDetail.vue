<template>
  <div class="album-detail">
    <div class="header">
      <div class="header-left">
        <el-button @click="$router.back()" class="back-btn">
          ← 返回
        </el-button>
        <div class="album-info" v-if="album">
          <h2>{{ album.name }}</h2>
          <p class="description">{{ album.description || '无描述' }}</p>
          <div class="meta">
            <span class="type-badge" :class="album.type">
              <span v-if="album.type === 'photo'">📷 照片相册</span>
              <span v-else-if="album.type === 'video'">🎬 视频相册</span>
              <span v-else>📁 混合相册</span>
            </span>
            <span class="media-count">{{ album.media_count }} 个文件</span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <el-button @click="showAddMediaDialog = true">
          ➕ 添加媒体
        </el-button>
      </div>
    </div>

    <!-- 媒体文件网格 -->
    <div class="media-grid" v-loading="loading">
      <div 
        v-for="media in mediaFiles" 
        :key="media.id" 
        class="media-item"
      >
        <div class="media-preview">
          <img
            v-if="media.file_type === 'image'"
            :src="`${UPLOAD_BASE_URL}/${media.file_path}`"
            :alt="media.caption"
            @click="openPreview(media)"
            class="clickable-media"
          />
          <video
            v-else-if="media.file_type === 'video'"
            :src="`${UPLOAD_BASE_URL}/${media.file_path}`"
            @click="openPreview(media)"
            preload="metadata"
            class="clickable-media"
          >
            您的浏览器不支持视频播放
          </video>
        </div>
        <div class="media-info">
          <p class="caption">{{ media.caption || '无描述' }}</p>
          <div class="media-meta">
            <span class="add-date">{{ formatDate(media.added_at) }}</span>
            <el-button 
              size="small" 
              type="danger" 
              @click="removeMedia(media.id)"
              class="remove-btn"
            >
              移除
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && mediaFiles.length === 0" class="empty-state">
      <div class="empty-icon">📁</div>
      <p>相册中还没有媒体文件</p>
      <el-button type="primary" @click="showAddMediaDialog = true">
        添加第一个文件
      </el-button>
    </div>

    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="prev, pager, next, total"
      @current-change="handlePageChange"
      class="pagination"
    />

    <!-- 添加媒体对话框 -->
    <el-dialog 
      title="添加媒体到相册" 
      v-model="showAddMediaDialog"
      width="800px"
    >
      <div class="add-media-content">
        <div class="available-media" v-loading="loadingAvailableMedia">
          <h4>选择要添加的媒体文件：</h4>
          <div class="media-selection-grid">
            <div
              v-for="media in availableMedia"
              :key="media.id"
              class="selectable-media"
              :class="{ selected: selectedMediaIds.includes(media.id) }"
              @click="toggleMediaSelection(media.id)"
            >
              <div class="media-preview-small">
                <img
                  v-if="media.file_type === 'image'"
                  :src="`${UPLOAD_BASE_URL}/${media.file_path}`"
                  :alt="media.caption"
                />
                <video
                  v-else-if="media.file_type === 'video'"
                  :src="`${UPLOAD_BASE_URL}/${media.file_path}`"
                  preload="metadata"
                >
                </video>
              </div>
              <div class="selection-overlay">
                <el-icon v-if="selectedMediaIds.includes(media.id)">
                  <Check />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="showAddMediaDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="addSelectedMedia" 
          :loading="adding"
          :disabled="selectedMediaIds.length === 0"
        >
          添加选中的文件 ({{ selectedMediaIds.length }})
        </el-button>
      </template>
    </el-dialog>

    <!-- 媒体预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="previewMedia?.caption || '媒体预览'"
      width="90%"
      center
      class="media-preview-dialog"
    >
      <div class="media-preview-container">
        <div class="media-viewer">
          <img
            v-if="previewMedia?.file_type === 'image'"
            :src="`${UPLOAD_BASE_URL}/${previewMedia.file_path}`"
            :alt="previewMedia.caption"
            class="preview-image"
          />
          <video
            v-else-if="previewMedia?.file_type === 'video'"
            :src="`${UPLOAD_BASE_URL}/${previewMedia.file_path}`"
            controls
            autoplay
            class="preview-video"
            ref="previewVideoRef"
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <div class="media-info-panel">
          <div class="info-item">
            <span class="label">描述：</span>
            <span class="value">{{ previewMedia?.caption || '无描述' }}</span>
          </div>
          <div class="info-item">
            <span class="label">类型：</span>
            <span class="value">{{ previewMedia?.file_type === 'image' ? '图片' : '视频' }}</span>
          </div>
          <div class="info-item">
            <span class="label">添加时间：</span>
            <span class="value">{{ formatDate(previewMedia?.added_at) }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="preview-controls">
          <el-button @click="closePreview">关闭</el-button>
          <el-button
            type="danger"
            @click="removeMediaFromPreview"
            :loading="removing"
          >
            从相册中移除
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import api from '../api/index.js'

// 环境变量
const UPLOAD_BASE_URL = import.meta.env.VITE_UPLOAD_BASE_URL || 'http://localhost:8080'

const route = useRoute()
const router = useRouter()

// 响应式数据
const album = ref(null)
const mediaFiles = ref([])
const availableMedia = ref([])
const loading = ref(false)
const loadingAvailableMedia = ref(false)
const adding = ref(false)
const showAddMediaDialog = ref(false)
const selectedMediaIds = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const showPreviewDialog = ref(false)
const previewMedia = ref(null)
const previewVideoRef = ref(null)
const removing = ref(false)

const albumId = computed(() => route.params.id)

// 加载相册信息
const loadAlbum = async () => {
  try {
    const response = await api.get(`/albums/${albumId.value}`)
    album.value = response.data
  } catch (error) {
    ElMessage.error('加载相册信息失败')
    console.error('Load album error:', error)
  }
}

// 加载相册媒体文件
const loadAlbumMedia = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    }

    const response = await api.get(`/albums/${albumId.value}/media`, { params })
    mediaFiles.value = response.data?.media_files || []
    total.value = response.data?.total || 0
  } catch (error) {
    ElMessage.error('加载相册媒体失败')
    console.error('Load album media error:', error)
  } finally {
    loading.value = false
  }
}

// 加载可用的媒体文件
const loadAvailableMedia = async () => {
  loadingAvailableMedia.value = true
  try {
    const params = { limit: 100 }

    // 根据相册类型过滤媒体文件
    if (album.value?.type === 'photo') {
      params.type = 'image'
    } else if (album.value?.type === 'video') {
      params.type = 'video'
    }

    const response = await api.get('/media', { params })
    availableMedia.value = response.data?.files || []
  } catch (error) {
    ElMessage.error('加载可用媒体失败')
    console.error('Load available media error:', error)
  } finally {
    loadingAvailableMedia.value = false
  }
}

// 切换媒体选择
const toggleMediaSelection = (mediaId) => {
  const index = selectedMediaIds.value.indexOf(mediaId)
  if (index > -1) {
    selectedMediaIds.value.splice(index, 1)
  } else {
    selectedMediaIds.value.push(mediaId)
  }
}

// 添加选中的媒体
const addSelectedMedia = async () => {
  if (selectedMediaIds.value.length === 0) return

  adding.value = true
  try {
    const response = await api.post(`/albums/${albumId.value}/media`, {
      media_ids: selectedMediaIds.value
    })

    ElMessage.success(`成功添加 ${response.data?.added_count || selectedMediaIds.value.length} 个文件`)
    showAddMediaDialog.value = false
    selectedMediaIds.value = []
    loadAlbumMedia()
    loadAlbum() // 重新加载相册信息以更新文件数量
  } catch (error) {
    ElMessage.error('添加媒体失败')
    console.error('Add media error:', error)
  } finally {
    adding.value = false
  }
}

// 从相册中移除媒体
const removeMedia = async (mediaId) => {
  try {
    await ElMessageBox.confirm('确定要从相册中移除这个文件吗？', '确认移除', {
      type: 'warning'
    })

    await api.delete(`/albums/${albumId.value}/media/${mediaId}`)
    ElMessage.success('文件移除成功')
    loadAlbumMedia()
    loadAlbum() // 重新加载相册信息以更新文件数量
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除文件失败')
      console.error('Remove media error:', error)
    }
  }
}

// 预览媒体
const openPreview = (media) => {
  previewMedia.value = media
  showPreviewDialog.value = true
}

// 关闭预览
const closePreview = () => {
  // 如果是视频，先暂停
  if (previewVideoRef.value) {
    previewVideoRef.value.pause()
    previewVideoRef.value.currentTime = 0
  }
  showPreviewDialog.value = false
  previewMedia.value = null
}

// 从预览中移除媒体
const removeMediaFromPreview = async () => {
  if (!previewMedia.value) return

  try {
    await ElMessageBox.confirm('确定要从相册中移除这个文件吗？', '确认移除', {
      type: 'warning'
    })

    removing.value = true
    await api.delete(`/albums/${albumId.value}/media/${previewMedia.value.id}`)
    ElMessage.success('文件移除成功')

    // 关闭预览对话框
    closePreview()

    // 重新加载数据
    loadAlbumMedia()
    loadAlbum()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除文件失败')
      console.error('Remove media error:', error)
    }
  } finally {
    removing.value = false
  }
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  loadAlbumMedia()
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 监听添加媒体对话框打开
const handleAddMediaDialog = () => {
  if (showAddMediaDialog.value) {
    loadAvailableMedia()
  }
}

// 监听预览对话框关闭，暂停视频播放
watch(showPreviewDialog, (newVal) => {
  if (!newVal && previewVideoRef.value) {
    // 对话框关闭时暂停视频
    previewVideoRef.value.pause()
    previewVideoRef.value.currentTime = 0 // 重置播放进度
  }
})

// 当预览媒体改变时，确保之前的视频被暂停
watch(previewMedia, (newMedia, oldMedia) => {
  if (oldMedia && previewVideoRef.value) {
    previewVideoRef.value.pause()
    previewVideoRef.value.currentTime = 0
  }
})

// 组件挂载时加载数据
onMounted(() => {
  loadAlbum()
  loadAlbumMedia()
})

// 监听对话框状态变化
watch(showAddMediaDialog, handleAddMediaDialog)
</script>

<style scoped>
.album-detail {
  max-width: 1400px;
  width: 90%;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.back-btn {
  margin-top: 5px;
}

.album-info h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.description {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.meta {
  display: flex;
  gap: 15px;
  align-items: center;
}

.type-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.type-badge.photo {
  background: #e1f3d8;
  color: #67c23a;
}

.type-badge.video {
  background: #fdf6ec;
  color: #e6a23c;
}

.type-badge.mixed {
  background: #ecf5ff;
  color: #409eff;
}

.media-count {
  color: #909399;
  font-size: 14px;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.media-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.media-preview {
  height: 200px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;
}

.media-preview img,
.media-preview video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.clickable-media {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.clickable-media:hover {
  transform: scale(1.05);
}

.media-info {
  padding: 12px;
}

.caption {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.media-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-date {
  color: #909399;
  font-size: 12px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.add-media-content {
  max-height: 500px;
  overflow-y: auto;
}

.media-selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 10px;
  margin-top: 15px;
}

.selectable-media {
  position: relative;
  border: 2px solid transparent;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.selectable-media:hover {
  border-color: #409eff;
}

.selectable-media.selected {
  border-color: #67c23a;
}

.media-preview-small {
  height: 100px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.media-preview-small img,
.media-preview-small video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.selection-overlay {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: #67c23a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.selectable-media.selected .selection-overlay {
  opacity: 1;
}

@media (max-width: 768px) {
  .album-detail {
    width: 95%;
    padding: 0 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .header-left {
    flex-direction: column;
    gap: 10px;
  }
  
  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }
}

/* 媒体预览对话框样式 */
.media-preview-dialog .el-dialog__body {
  padding: 20px;
}

.media-preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.media-viewer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
}

.preview-video {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}

.media-info-panel {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.info-item .value {
  color: #6c757d;
  flex: 1;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
