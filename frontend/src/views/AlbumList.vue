<template>
  <div class="album-list">
    <div class="header">
      <h2>📚 相册管理</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        ➕ 创建相册
      </el-button>
    </div>

    <!-- 相册类型筛选 -->
    <el-radio-group v-model="filterType" @change="loadAlbums" class="type-filter">
      <el-radio-button label="">全部</el-radio-button>
      <el-radio-button label="photo">📷 照片相册</el-radio-button>
      <el-radio-button label="video">🎬 视频相册</el-radio-button>
      <el-radio-button label="mixed">📁 混合相册</el-radio-button>
    </el-radio-group>

    <!-- 相册网格 -->
    <div class="album-grid" v-loading="loading">
      <div 
        v-for="album in albums" 
        :key="album.id" 
        class="album-card"
        @click="viewAlbum(album.id)"
      >
        <div class="album-cover">
          <img
            v-if="album.cover_image"
            :src="`${UPLOAD_BASE_URL}/${album.cover_image}`"
            :alt="album.name"
          />
          <div v-else class="default-cover">
            <span v-if="album.type === 'photo'">📷</span>
            <span v-else-if="album.type === 'video'">🎬</span>
            <span v-else>📁</span>
          </div>
        </div>
        <div class="album-info">
          <h3>{{ album.name }}</h3>
          <p class="description">{{ album.description || '无描述' }}</p>
          <div class="album-meta">
            <span class="media-count">{{ album.media_count }} 个文件</span>
            <span class="create-date">{{ formatDate(album.created_at) }}</span>
          </div>
        </div>
        <div class="album-actions" @click.stop>
          <el-button size="small" @click="editAlbum(album)">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteAlbum(album.id)">删除</el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <el-pagination
      v-if="total > 0"
      :current-page="currentPage"
      :page-size="pageSize"
      :total="total"
      layout="prev, pager, next, total"
      @current-change="handlePageChange"
      class="pagination"
    />

    <!-- 创建/编辑相册对话框 -->
    <el-dialog 
      :title="editingAlbum ? '编辑相册' : '创建相册'" 
      v-model="showCreateDialog"
      width="500px"
    >
      <el-form :model="albumForm" :rules="albumRules" ref="albumFormRef" label-width="80px">
        <el-form-item label="相册名称" prop="name">
          <el-input v-model="albumForm.name" placeholder="请输入相册名称" />
        </el-form-item>
        <el-form-item label="相册描述" prop="description">
          <el-input 
            v-model="albumForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入相册描述（可选）" 
          />
        </el-form-item>
        <el-form-item label="相册类型" prop="type">
          <el-select v-model="albumForm.type" placeholder="请选择相册类型">
            <el-option label="📷 照片相册" value="photo" />
            <el-option label="🎬 视频相册" value="video" />
            <el-option label="📁 混合相册" value="mixed" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAlbum" :loading="saving">
          {{ editingAlbum ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '../api/index.js'

// 环境变量
const UPLOAD_BASE_URL = import.meta.env.VITE_UPLOAD_BASE_URL || 'http://localhost:8080'

const router = useRouter()

// 响应式数据
const albums = ref([])
const loading = ref(false)
const saving = ref(false)
const showCreateDialog = ref(false)
const editingAlbum = ref(null)
const filterType = ref('')
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

// 表单数据
const albumForm = reactive({
  name: '',
  description: '',
  type: 'mixed'
})

// 表单验证规则
const albumRules = {
  name: [
    { required: true, message: '请输入相册名称', trigger: 'blur' },
    { min: 1, max: 50, message: '相册名称长度在 1 到 50 个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择相册类型', trigger: 'change' }
  ]
}

const albumFormRef = ref()

// 加载相册列表
const loadAlbums = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    }
    if (filterType.value) {
      params.type = filterType.value
    }

    const response = await api.get('/albums', { params })
    albums.value = response.data?.albums || []
    total.value = response.data?.total || 0
  } catch (error) {
    ElMessage.error('加载相册列表失败')
    console.error('Load albums error:', error)
  } finally {
    loading.value = false
  }
}

// 查看相册详情
const viewAlbum = (albumId) => {
  router.push(`/albums/${albumId}`)
}

// 编辑相册
const editAlbum = (album) => {
  editingAlbum.value = album
  albumForm.name = album.name
  albumForm.description = album.description
  albumForm.type = album.type
  showCreateDialog.value = true
}

// 保存相册
const saveAlbum = async () => {
  if (!albumFormRef.value) return
  
  try {
    await albumFormRef.value.validate()
    saving.value = true

    if (editingAlbum.value) {
      // 更新相册
      await api.put(`/albums/${editingAlbum.value.id}`, albumForm)
      ElMessage.success('相册更新成功')
    } else {
      // 创建相册
      await api.post('/albums', albumForm)
      ElMessage.success('相册创建成功')
    }

    showCreateDialog.value = false
    resetForm()
    loadAlbums()
  } catch (error) {
    ElMessage.error(editingAlbum.value ? '更新相册失败' : '创建相册失败')
    console.error('Save album error:', error)
  } finally {
    saving.value = false
  }
}

// 删除相册
const deleteAlbum = async (albumId) => {
  try {
    await ElMessageBox.confirm('确定要删除这个相册吗？', '确认删除', {
      type: 'warning'
    })

    await api.delete(`/albums/${albumId}`)
    ElMessage.success('相册删除成功')
    loadAlbums()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除相册失败')
      console.error('Delete album error:', error)
    }
  }
}

// 重置表单
const resetForm = () => {
  editingAlbum.value = null
  albumForm.name = ''
  albumForm.description = ''
  albumForm.type = 'mixed'
  if (albumFormRef.value) {
    albumFormRef.value.resetFields()
  }
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  loadAlbums()
}

// 格式化日期
const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadAlbums()
})
</script>

<style scoped>
.album-list {
  max-width: 1400px;
  width: 90%;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #333;
}

.type-filter {
  margin-bottom: 20px;
}

.album-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.album-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.album-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.album-cover {
  height: 200px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.album-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-cover {
  font-size: 48px;
  color: #909399;
}

.album-info {
  padding: 15px;
}

.album-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.description {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.album-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.album-actions {
  padding: 10px 15px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .album-list {
    width: 95%;
    padding: 0 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .album-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }
}
</style>
