<template>
  <div class="user-management">
    <div class="header">
      <h1>用户管理</h1>
      <el-button @click="loadUsers" :loading="loading" type="primary">
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <el-card class="user-list-card">
      <el-table 
        :data="users" 
        v-loading="loading"
        style="width: 100%"
        :default-sort="{ prop: 'created_at', order: 'descending' }"
      >
        <el-table-column prop="id" label="ID" width="80" sortable />
        <el-table-column prop="username" label="用户名" width="150" sortable />
        <el-table-column prop="email" label="邮箱" width="200" sortable />
        <el-table-column prop="status" label="状态" width="120" sortable>
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="注册时间" width="180" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="最后更新" width="180" sortable>
          <template #default="scope">
            {{ formatDate(scope.row.updated_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <!-- 审批按钮 -->
              <el-button
                v-if="scope.row.status === 'pending'"
                type="success"
                size="small"
                @click="approveUser(scope.row)"
                :loading="approving === scope.row.id"
              >
                通过
              </el-button>

              <!-- 拒绝按钮 -->
              <el-button
                v-if="scope.row.status === 'pending'"
                type="warning"
                size="small"
                @click="rejectUser(scope.row)"
                :loading="rejecting === scope.row.id"
              >
                拒绝
              </el-button>

              <!-- 删除按钮 -->
              <el-button
                type="danger"
                size="small"
                @click="confirmDeleteUser(scope.row)"
                :disabled="scope.row.id === currentUserId"
              >
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 删除确认对话框 -->
    <el-dialog
      v-model="deleteDialogVisible"
      title="确认删除用户"
      width="400px"
      :before-close="handleClose"
    >
      <div class="delete-warning">
        <el-icon class="warning-icon"><Warning /></el-icon>
        <div class="warning-text">
          <p><strong>警告：此操作不可撤销！</strong></p>
          <p>您即将删除用户：<strong>{{ selectedUser?.username }}</strong></p>
          <p>这将同时删除该用户的：</p>
          <ul>
            <li>所有日记记录</li>
            <li>所有上传的媒体文件</li>
            <li>所有相关数据</li>
          </ul>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button 
            type="danger" 
            @click="deleteUser"
            :loading="deleting"
          >
            确认删除
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import { Refresh, Warning } from '@element-plus/icons-vue'
import Message from '../utils/message'
import { useUserStore } from '../stores/user'

const userStore = useUserStore()

// 响应式数据
const users = ref([])
const loading = ref(false)
const deleting = ref(false)
const approving = ref(null) // 正在审批的用户ID
const rejecting = ref(null) // 正在拒绝的用户ID
const deleteDialogVisible = ref(false)
const selectedUser = ref(null)

// 计算属性
const currentUserId = computed(() => {
  return userStore.user.value?.id
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取状态类型（用于el-tag的type属性）
const getStatusType = (status) => {
  switch (status) {
    case 'approved': return 'success'
    case 'pending': return 'warning'
    case 'rejected': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'approved': return '已通过'
    case 'pending': return '待审批'
    case 'rejected': return '已拒绝'
    default: return '未知'
  }
}

// 加载用户列表
const loadUsers = async () => {
  loading.value = true
  try {
    const token = userStore.token.value
    const response = await fetch('/api/v1/users', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    const data = await response.json()
    if (data.code === 200) {
      users.value = data.data.users || []
    } else {
      Message.error(data.message || '获取用户列表失败')
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    Message.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 确认删除用户
const confirmDeleteUser = (user) => {
  selectedUser.value = user
  deleteDialogVisible.value = true
}

// 删除用户
const deleteUser = async () => {
  if (!selectedUser.value) return
  
  deleting.value = true
  try {
    const token = userStore.token.value
    const response = await fetch(`/api/v1/users/${selectedUser.value.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    const data = await response.json()
    if (data.code === 200) {
      Message.success('用户删除成功')
      deleteDialogVisible.value = false
      selectedUser.value = null
      // 重新加载用户列表
      await loadUsers()
    } else {
      Message.error(data.message || '删除用户失败')
    }
  } catch (error) {
    console.error('删除用户失败:', error)
    Message.error('删除用户失败')
  } finally {
    deleting.value = false
  }
}

// 审批用户
const approveUser = async (user) => {
  approving.value = user.id
  try {
    const token = userStore.token.value
    const response = await fetch(`/api/v1/users/${user.id}/approve`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    const data = await response.json()
    if (data.code === 200) {
      Message.success('用户审批成功')
      // 重新加载用户列表
      await loadUsers()
    } else {
      Message.error(data.message || '审批用户失败')
    }
  } catch (error) {
    console.error('审批用户失败:', error)
    Message.error('审批用户失败')
  } finally {
    approving.value = null
  }
}

// 拒绝用户
const rejectUser = async (user) => {
  rejecting.value = user.id
  try {
    const token = userStore.token.value
    const response = await fetch(`/api/v1/users/${user.id}/reject`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })

    const data = await response.json()
    if (data.code === 200) {
      Message.success('用户已被拒绝')
      // 重新加载用户列表
      await loadUsers()
    } else {
      Message.error(data.message || '拒绝用户失败')
    }
  } catch (error) {
    console.error('拒绝用户失败:', error)
    Message.error('拒绝用户失败')
  } finally {
    rejecting.value = null
  }
}

// 处理对话框关闭
const handleClose = (done) => {
  if (deleting.value) {
    Message.warning('正在删除用户，请稍候...')
    return
  }
  done()
}

// 组件挂载时加载数据
onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  padding: 20px;
  max-width: 95vw;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.user-list-card {
  margin-bottom: 20px;
}

.delete-warning {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.warning-icon {
  color: #f56c6c;
  font-size: 24px;
  margin-top: 2px;
}

.warning-text {
  flex: 1;
}

.warning-text p {
  margin: 8px 0;
}

.warning-text ul {
  margin: 8px 0;
  padding-left: 20px;
}

.warning-text li {
  margin: 4px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-management {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }
  
  .header h1 {
    text-align: center;
    font-size: 20px;
  }
}
</style>
