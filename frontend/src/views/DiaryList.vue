<template>
  <div class="diary-list">
    <div class="header-actions">
      <h2>📖 我们的日记</h2>
      <el-button type="primary" @click="$router.push('/diary/new')">
        ✍️ 写日记
      </el-button>
    </div>

    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索日记..."
        @input="handleSearch"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>

    <div class="diary-grid" v-loading="loading">
      <div
        v-for="diary in diaries"
        :key="diary.id"
        class="diary-card"
        @click="$router.push(`/diary/${diary.id}`)"
      >
        <div class="diary-header">
          <h3>{{ diary.title }}</h3>
          <span class="diary-date">{{ formatDate(diary.created_at) }}</span>
        </div>
        <div class="diary-content">
          {{ diary.content.substring(0, 100) }}{{ diary.content.length > 100 ? '...' : '' }}
        </div>
        <div class="diary-meta">
          <span v-if="diary.mood" class="mood">{{ diary.mood }}</span>
          <span v-if="diary.weather" class="weather">{{ diary.weather }}</span>
          <span v-if="diary.location" class="location">📍 {{ diary.location }}</span>
        </div>
      </div>
    </div>

    <div v-if="diaries.length === 0 && !loading" class="empty-state">
      <p>还没有日记，开始记录美好回忆吧！</p>
      <el-button type="primary" @click="$router.push('/diary/new')">
        写第一篇日记
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { diaryAPI } from '../api'
import Message from '../utils/message'

const diaries = ref([])
const loading = ref(false)
const searchKeyword = ref('')

const loadDiaries = async () => {
  try {
    loading.value = true
    const response = await diaryAPI.getDiaries({
      keyword: searchKeyword.value
    })

    if (response.code === 200) {
      diaries.value = response.data.diaries || []
    }
  } catch (error) {
    console.error('Load diaries error:', error)
    Message.error('加载日记失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  loadDiaries()
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

onMounted(() => {
  loadDiaries()
})
</script>

<style scoped>
.diary-list {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions h2 {
  color: white;
  font-size: 24px;
}

.search-bar {
  margin-bottom: 30px;
}

.diary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.diary-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.diary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.diary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.diary-header h3 {
  color: #333;
  font-size: 18px;
  margin: 0;
  flex: 1;
}

.diary-date {
  color: #999;
  font-size: 12px;
  white-space: nowrap;
  margin-left: 10px;
}

.diary-content {
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.diary-meta {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.mood, .weather, .location {
  background: #f0f0f0;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: #666;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 20px;
}
</style>