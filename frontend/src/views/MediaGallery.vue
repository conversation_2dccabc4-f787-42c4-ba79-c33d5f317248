<template>
  <div class="media-gallery">
    <div class="gallery-header">
      <h2>📸 我们的相册</h2>
      <div class="header-actions">
        <el-upload
          :action="`/api/v1/media/upload`"
          :headers="uploadHeaders"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeUpload"
          :show-file-list="false"
          accept="image/*,video/*"
          class="upload-btn"
        >
          <el-button type="primary">
            📤 上传文件
          </el-button>
        </el-upload>
      </div>
    </div>

    <div class="filter-bar">
      <el-radio-group v-model="currentFilter" @change="loadMediaFiles">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button label="image">📷 照片</el-radio-button>
        <el-radio-button label="video">🎬 视频</el-radio-button>
      </el-radio-group>
    </div>

    <div class="media-grid" v-loading="loading">
      <div
        v-for="file in mediaFiles"
        :key="file.id"
        class="media-item"
        @click="viewMedia(file)"
      >
        <div class="media-preview">
          <img
            v-if="file.file_type === 'image'"
            :src="`${UPLOAD_BASE_URL}/uploads/${file.file_name}`"
            :alt="file.caption || '照片'"
            class="media-image"
          />
          <div v-else class="video-preview">
            <video
              :src="`${UPLOAD_BASE_URL}/uploads/${file.file_name}`"
              class="media-video"
              preload="metadata"
            />
            <div class="video-overlay">
              <el-icon class="play-icon"><VideoPlay /></el-icon>
            </div>
          </div>
        </div>

        <div class="media-info">
          <p class="media-caption">{{ file.caption || '无描述' }}</p>
          <p class="media-date">{{ formatDate(file.created_at) }}</p>
        </div>

        <div class="media-actions">
          <el-button @click.stop="editCaption(file)" type="text" size="small">
            编辑
          </el-button>
          <el-button @click.stop="deleteMedia(file)" type="text" size="small" class="delete-btn">
            删除
          </el-button>
        </div>
      </div>
    </div>

    <div v-if="mediaFiles.length === 0 && !loading" class="empty-state">
      <p>还没有上传任何文件</p>
      <el-upload
        :action="`/api/v1/media/upload`"
        :headers="uploadHeaders"
        :on-success="handleUploadSuccess"
        :on-error="handleUploadError"
        :before-upload="beforeUpload"
        :show-file-list="false"
        accept="image/*,video/*"
      >
        <el-button type="primary" size="large">
          上传第一个文件
        </el-button>
      </el-upload>
    </div>

    <!-- 媒体查看对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      :title="currentMedia.caption || '媒体文件'"
      width="90%"
      center
      class="media-preview-dialog"
    >
      <div class="media-preview-container">
        <div class="media-viewer">
          <img
            v-if="currentMedia.file_type === 'image'"
            :src="`${UPLOAD_BASE_URL}/uploads/${currentMedia.file_name}`"
            class="preview-image"
            :alt="currentMedia.caption"
          />
          <video
            v-else
            ref="videoPlayer"
            :src="`${UPLOAD_BASE_URL}/uploads/${currentMedia.file_name}`"
            controls
            autoplay
            class="preview-video"
          />
        </div>

        <div class="media-info-panel">
          <div class="info-item">
            <span class="label">描述：</span>
            <span class="value">{{ currentMedia.caption || '无描述' }}</span>
          </div>
          <div class="info-item">
            <span class="label">类型：</span>
            <span class="value">{{ currentMedia.file_type === 'image' ? '图片' : '视频' }}</span>
          </div>
          <div class="info-item">
            <span class="label">上传时间：</span>
            <span class="value">{{ formatDate(currentMedia.created_at) }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="preview-controls">
          <el-button @click="closePreview">关闭</el-button>
          <el-button type="primary" @click="editCaption(currentMedia)">
            编辑描述
          </el-button>
          <el-button
            type="danger"
            @click="deleteMedia(currentMedia)"
          >
            删除文件
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 编辑描述对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑描述"
      width="400px"
      center
    >
      <el-input
        v-model="editCaptionText"
        type="textarea"
        :rows="3"
        placeholder="为这个文件添加描述..."
      />
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCaption" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessageBox } from 'element-plus'
import { VideoPlay } from '@element-plus/icons-vue'
import Message from '../utils/message'
import { mediaAPI } from '../api'

// 环境变量
const UPLOAD_BASE_URL = import.meta.env.VITE_UPLOAD_BASE_URL || ''

const loading = ref(false)
const saving = ref(false)
const mediaFiles = ref([])
const currentFilter = ref('')
const viewDialogVisible = ref(false)
const editDialogVisible = ref(false)
const currentMedia = ref({})
const editCaptionText = ref('')
const editingFile = ref(null)
const videoPlayer = ref(null)

const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}))

const loadMediaFiles = async () => {
  try {
    loading.value = true
    const response = await mediaAPI.getMediaFiles({
      type: currentFilter.value
    })

    if (response.code === 200) {
      mediaFiles.value = response.data.files || []
    }
  } catch (error) {
    console.error('Load media files error:', error)
    Message.error('加载文件失败')
  } finally {
    loading.value = false
  }
}

const beforeUpload = (file) => {
  const isValidType = file.type.startsWith('image/') || file.type.startsWith('video/')

  if (!isValidType) {
    Message.error('只能上传图片或视频文件')
    return false
  }
  // 移除文件大小限制
  return true
}

const handleUploadSuccess = (response) => {
  if (response.code === 201) {
    Message.success('上传成功')
    loadMediaFiles()
  } else {
    Message.error(response.message || '上传失败')
  }
}

const handleUploadError = () => {
  ElMessage.error('上传失败，请检查网络连接')
}

const viewMedia = (file) => {
  currentMedia.value = file
  viewDialogVisible.value = true
}

// 关闭预览
const closePreview = () => {
  // 如果是视频，先暂停
  if (videoPlayer.value) {
    videoPlayer.value.pause()
    videoPlayer.value.currentTime = 0
  }
  viewDialogVisible.value = false
  currentMedia.value = {}
}

// 监听对话框关闭，暂停视频播放
watch(viewDialogVisible, (newVal) => {
  if (!newVal && videoPlayer.value) {
    videoPlayer.value.pause()
    videoPlayer.value.currentTime = 0 // 重置播放进度
  }
})

// 当媒体改变时，确保之前的视频被暂停
watch(currentMedia, (newMedia, oldMedia) => {
  if (oldMedia && videoPlayer.value) {
    videoPlayer.value.pause()
    videoPlayer.value.currentTime = 0
  }
})

const editCaption = (file) => {
  editingFile.value = file
  editCaptionText.value = file.caption || ''
  editDialogVisible.value = true
}

const saveCaption = async () => {
  try {
    saving.value = true
    const response = await mediaAPI.updateCaption(editingFile.value.id, {
      caption: editCaptionText.value
    })

    if (response.code === 200) {
      Message.success('更新成功')
      editDialogVisible.value = false
      loadMediaFiles()
    } else {
      Message.error(response.message || '更新失败')
    }
  } catch (error) {
    console.error('Update caption error:', error)
    Message.error('更新失败')
  } finally {
    saving.value = false
  }
}

const deleteMedia = async (file) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个文件吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const response = await mediaAPI.deleteMediaFile(file.id)

    if (response.code === 200) {
      Message.success('删除成功')
      loadMediaFiles()
    } else {
      Message.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete media error:', error)
      Message.error('删除失败')
    }
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

onMounted(() => {
  loadMediaFiles()
})
</script>

<style scoped>
.media-gallery {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
}

.gallery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.gallery-header h2 {
  color: white;
  font-size: 24px;
  margin: 0;
}

.filter-bar {
  margin-bottom: 30px;
  text-align: center;
}

.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  padding: 0;
}

.media-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.media-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.media-preview {
  position: relative;
  width: 100%;
  height: 240px;
  overflow: hidden;
}

.media-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.media-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.play-icon {
  font-size: 48px;
  color: white;
  opacity: 0.8;
}

.media-info {
  padding: 15px;
}

.media-caption {
  color: #333;
  font-size: 14px;
  margin: 0 0 5px 0;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-date {
  color: #999;
  font-size: 12px;
  margin: 0;
}

.media-actions {
  padding: 0 15px 15px;
  display: flex;
  justify-content: space-between;
}

.delete-btn {
  color: #f56c6c !important;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 20px;
}

.media-viewer {
  text-align: center;
}

.viewer-image {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 10px;
}

.viewer-video {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 10px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-upload) {
  display: inline-block;
}

/* 超大屏幕优化 */
@media (min-width: 1600px) {
  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
  }

  .media-preview {
    height: 280px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1400px) and (max-width: 1599px) {
  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 22px;
  }

  .media-preview {
    height: 260px;
  }
}

/* 中等屏幕 */
@media (max-width: 1200px) {
  .media-gallery {
    padding: 0 15px;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }
}

/* 平板设备 */
@media (max-width: 768px) {
  .media-gallery {
    padding: 0 10px;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }

  .gallery-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .media-preview {
    height: 180px;
  }

  .play-icon {
    font-size: 36px;
  }
}

/* 手机设备 */
@media (max-width: 480px) {
  .media-gallery {
    padding: 0 5px;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
  }

  .media-preview {
    height: 150px;
  }
}

/* 媒体预览对话框样式 */
.media-preview-dialog .el-dialog__body {
  padding: 20px;
}

.media-preview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.media-viewer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
}

.preview-video {
  max-width: 100%;
  max-height: 70vh;
  border-radius: 8px;
}

.media-info-panel {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 600;
  color: #495057;
  min-width: 80px;
}

.info-item .value {
  color: #6c757d;
  flex: 1;
}

.preview-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>