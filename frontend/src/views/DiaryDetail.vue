<template>
  <div class="diary-detail" v-loading="loading">
    <div class="detail-header">
      <el-button @click="$router.back()" type="text" class="back-btn">
        ← 返回
      </el-button>
      <div class="header-actions" v-if="diary.id">
        <el-button @click="editDiary" type="primary" plain>
          ✏️ 编辑
        </el-button>
        <el-button @click="deleteDiary" type="danger" plain>
          🗑️ 删除
        </el-button>
      </div>
    </div>

    <div class="diary-content" v-if="diary.id">
      <div class="diary-header">
        <h1 class="diary-title">{{ diary.title }}</h1>
        <div class="diary-meta">
          <span class="diary-date">{{ formatDate(diary.created_at) }}</span>
          <span v-if="diary.updated_at !== diary.created_at" class="updated-date">
            (已编辑于 {{ formatDate(diary.updated_at) }})
          </span>
        </div>
      </div>

      <div class="diary-tags" v-if="diary.mood || diary.weather || diary.location">
        <el-tag v-if="diary.mood" type="success" class="tag">
          {{ diary.mood }}
        </el-tag>
        <el-tag v-if="diary.weather" type="info" class="tag">
          {{ diary.weather }}
        </el-tag>
        <el-tag v-if="diary.location" type="warning" class="tag">
          📍 {{ diary.location }}
        </el-tag>
        <el-tag v-if="diary.is_private" type="danger" class="tag">
          🔒 私密
        </el-tag>
      </div>

      <div class="diary-body">
        <div class="content-text">
          {{ diary.content }}
        </div>
      </div>
    </div>

    <div class="empty-state" v-else-if="!loading">
      <p>日记不存在或已被删除</p>
      <el-button @click="$router.push('/diaries')" type="primary">
        返回日记列表
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import Message from '../utils/message'
import { diaryAPI } from '../api'

const router = useRouter()
const route = useRoute()

const loading = ref(false)
const diary = ref({})

const loadDiary = async () => {
  try {
    loading.value = true
    const response = await diaryAPI.getDiary(route.params.id)

    if (response.code === 200) {
      diary.value = response.data
    } else {
      Message.error('日记不存在')
      router.push('/diaries')
    }
  } catch (error) {
    console.error('Load diary error:', error)
    Message.error('加载日记失败')
    router.push('/diaries')
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const editDiary = () => {
  router.push(`/diary/${diary.value.id}/edit`)
}

const deleteDiary = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这篇日记吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    loading.value = true
    const response = await diaryAPI.deleteDiary(diary.value.id)

    if (response.code === 200) {
      Message.success('删除成功')
      router.push('/diaries')
    } else {
      Message.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete diary error:', error)
      Message.error('删除失败')
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDiary()
})
</script>

<style scoped>
.diary-detail {
  max-width: 1200px;
  width: 95%;
  margin: 0 auto;
  padding: 0 20px;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.back-btn {
  color: white !important;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.diary-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.diary-header {
  margin-bottom: 30px;
  text-align: center;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 20px;
}

.diary-title {
  color: #333;
  font-size: 32px;
  font-weight: 600;
  margin: 0 0 15px 0;
  line-height: 1.3;
}

.diary-meta {
  color: #666;
  font-size: 14px;
}

.updated-date {
  color: #999;
  font-size: 12px;
}

.diary-tags {
  margin-bottom: 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.tag {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 20px;
}

.diary-body {
  line-height: 1.8;
}

.content-text {
  color: #333;
  font-size: 16px;
  white-space: pre-wrap;
  word-wrap: break-word;
  text-align: left;
  background: #fafafa;
  padding: 30px;
  border-radius: 15px;
  border-left: 4px solid #409eff;
}

.empty-state {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.empty-state p {
  color: #666;
  font-size: 16px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .diary-content {
    padding: 20px;
  }

  .diary-title {
    font-size: 24px;
  }

  .content-text {
    padding: 20px;
    font-size: 15px;
  }

  .header-actions {
    flex-direction: column;
    gap: 5px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}
</style>