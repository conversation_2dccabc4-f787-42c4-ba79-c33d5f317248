<template>
  <div class="diary-edit">
    <div class="edit-header">
      <h2>{{ isEdit ? '✏️ 编辑日记' : '✍️ 写日记' }}</h2>
      <el-button @click="$router.back()" type="text">返回</el-button>
    </div>

    <div class="edit-form-container">
      <el-form
        ref="diaryFormRef"
        :model="diaryForm"
        :rules="diaryRules"
        class="diary-form"
        label-position="top"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="diaryForm.title"
            placeholder="给这篇日记起个标题..."
            size="large"
          />
        </el-form-item>

        <el-form-item label="内容" prop="content">
          <el-input
            v-model="diaryForm.content"
            type="textarea"
            :rows="10"
            placeholder="记录今天的美好回忆..."
            resize="none"
          />
        </el-form-item>

        <div class="form-row">
          <el-form-item label="心情" class="form-item">
            <el-select v-model="diaryForm.mood" placeholder="选择心情" clearable>
              <el-option label="😊 开心" value="开心" />
              <el-option label="😍 甜蜜" value="甜蜜" />
              <el-option label="🥰 幸福" value="幸福" />
              <el-option label="😌 平静" value="平静" />
              <el-option label="😔 难过" value="难过" />
              <el-option label="😤 生气" value="生气" />
              <el-option label="😴 疲惫" value="疲惫" />
              <el-option label="🤔 思考" value="思考" />
            </el-select>
          </el-form-item>

          <el-form-item label="天气" class="form-item">
            <el-select v-model="diaryForm.weather" placeholder="选择天气" clearable>
              <el-option label="☀️ 晴天" value="晴天" />
              <el-option label="⛅ 多云" value="多云" />
              <el-option label="🌧️ 雨天" value="雨天" />
              <el-option label="❄️ 雪天" value="雪天" />
              <el-option label="🌫️ 雾天" value="雾天" />
              <el-option label="⛈️ 雷雨" value="雷雨" />
            </el-select>
          </el-form-item>
        </div>

        <el-form-item label="地点">
          <el-input
            v-model="diaryForm.location"
            placeholder="在哪里呢？"
            prefix-icon="Location"
          />
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="diaryForm.is_private">
            🔒 设为私密日记
          </el-checkbox>
        </el-form-item>

        <div class="form-actions">
          <el-button @click="$router.back()" size="large">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="handleSave"
            :loading="loading"
            size="large"
          >
            {{ isEdit ? '更新' : '保存' }}
          </el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import Message from '../utils/message'
import { diaryAPI } from '../api'

const router = useRouter()
const route = useRoute()

const diaryFormRef = ref()
const loading = ref(false)

const isEdit = computed(() => !!route.params.id)

const diaryForm = reactive({
  title: '',
  content: '',
  mood: '',
  weather: '',
  location: '',
  is_private: false
})

const diaryRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { max: 100, message: '标题不能超过100个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' },
    { max: 5000, message: '内容不能超过5000个字符', trigger: 'blur' }
  ]
}

const loadDiary = async () => {
  if (!isEdit.value) return

  try {
    loading.value = true
    const response = await diaryAPI.getDiary(route.params.id)

    if (response.code === 200) {
      const diary = response.data
      Object.assign(diaryForm, {
        title: diary.title,
        content: diary.content,
        mood: diary.mood,
        weather: diary.weather,
        location: diary.location,
        is_private: diary.is_private
      })
    } else {
      Message.error('加载日记失败')
      router.back()
    }
  } catch (error) {
    console.error('Load diary error:', error)
    Message.error('加载日记失败')
    router.back()
  } finally {
    loading.value = false
  }
}

const handleSave = async () => {
  if (!diaryFormRef.value) return

  try {
    await diaryFormRef.value.validate()
    loading.value = true

    let response
    if (isEdit.value) {
      response = await diaryAPI.updateDiary(route.params.id, diaryForm)
    } else {
      response = await diaryAPI.createDiary(diaryForm)
    }

    if (response.code === 200 || response.code === 201) {
      Message.success(isEdit.value ? '更新成功' : '保存成功')
      router.push('/diaries')
    } else {
      Message.error(response.message || '保存失败')
    }
  } catch (error) {
    console.error('Save diary error:', error)
    Message.error('保存失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDiary()
})
</script>

<style scoped>
.diary-edit {
  max-width: 1400px;
  width: 90%;
  margin: 0 auto;
  padding: 0 20px;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.edit-header h2 {
  color: white;
  font-size: 24px;
  margin: 0;
}

.edit-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px 80px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: none;
}

.diary-form {
  max-width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-item {
  margin-bottom: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.form-actions .el-button {
  min-width: 100px;
}

:deep(.el-form-item__label) {
  color: #333;
  font-weight: 500;
  margin-bottom: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 10px;
  border: 1px solid #dcdfe6;
  font-family: inherit;
  line-height: 1.6;
}

:deep(.el-input__inner) {
  border-radius: 10px;
}

:deep(.el-select) {
  width: 100%;
}

/* 中等屏幕优化 */
@media (max-width: 1200px) {
  .diary-edit {
    width: 95%;
  }

  .edit-form-container {
    padding: 30px 50px;
  }
}

@media (max-width: 768px) {
  .diary-edit {
    width: 100%;
    padding: 0 10px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .form-item {
    margin-bottom: 18px;
  }

  .edit-form-container {
    padding: 20px 30px;
    border-radius: 15px;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .el-button {
    width: 100%;
  }
}

/* 手机屏幕优化 */
@media (max-width: 480px) {
  .edit-form-container {
    padding: 15px 20px;
    border-radius: 10px;
  }
}
</style>