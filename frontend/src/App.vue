<template>
  <div id="app">
    <el-container>
      <el-header v-if="$route.name !== 'Login' && $route.name !== 'Register'" class="header">
        <div class="header-content">
          <h1>💕 我们的回忆</h1>
          <div class="nav-menu">
            <router-link to="/diaries" class="nav-link">📖 日记</router-link>
            <router-link to="/media" class="nav-link">📸 媒体库</router-link>
            <router-link to="/albums" class="nav-link">📚 相册</router-link>
            <router-link to="/users" class="nav-link">👥 用户管理</router-link>
          </div>
          <div class="user-info">
            <span v-if="userStore.user.value">{{ userStore.user.value.username }}</span>
            <el-button @click="logout" type="text">退出</el-button>
          </div>
        </div>
      </el-header>

      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useUserStore } from './stores/user'

const router = useRouter()
const userStore = useUserStore()

const logout = () => {
  userStore.logout()
  router.push('/login')
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

#app {
  min-height: 100vh;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 20px;
}

.header-content h1 {
  color: #333;
  font-size: 24px;
}

.nav-menu {
  display: flex;
  gap: 20px;
}

.nav-link {
  color: #666;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(64, 158, 255, 0.1);
  color: #409eff;
}

.nav-link.router-link-active {
  background: #409eff;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.main-content {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
  width: 100%;
}
</style>