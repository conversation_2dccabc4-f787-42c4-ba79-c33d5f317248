#!/bin/bash

# 情侣日记应用构建脚本

echo "🚀 开始构建情侣日记应用..."

# 检查是否安装了必要的工具
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go"
    exit 1
fi

# 创建构建目录
echo "📁 创建构建目录..."
rm -rf dist
mkdir -p dist

# 构建前端
echo "🎨 构建前端应用..."
cd frontend
npm install
npm run build
cd ..

# 将前端构建结果复制到dist目录
echo "📦 复制前端构建结果..."
cp -r frontend/dist/* dist/

# 构建后端
echo "⚙️ 构建后端应用..."
cd backend
go mod tidy
go build -o ../dist/diary-server ./cmd/main.go
cd ..

# 复制必要的文件
echo "📋 复制配置文件..."
cp .env.production dist/.env
cp -r backend/uploads dist/ 2>/dev/null || mkdir -p dist/uploads

# 创建启动脚本
echo "📝 创建启动脚本..."
cat > dist/start.sh << 'EOF'
#!/bin/bash

# 设置环境变量
export GIN_MODE=release

# 加载环境变量
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# 启动服务器
echo "🚀 启动情侣日记服务器..."
./diary-server
EOF

chmod +x dist/start.sh

# 创建systemd服务文件（可选）
echo "🔧 创建systemd服务文件..."
cat > dist/diary.service << 'EOF'
[Unit]
Description=Diary App
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/app
ExecStart=/path/to/your/app/diary-server
Restart=always
RestartSec=5
Environment=GIN_MODE=release

[Install]
WantedBy=multi-user.target
EOF

echo "✅ 构建完成！"
echo ""
echo "📦 构建结果位于 dist/ 目录中"
echo "📁 包含以下文件："
echo "   - diary-server (后端可执行文件)"
echo "   - index.html 和静态资源 (前端文件)"
echo "   - .env (生产环境配置)"
echo "   - start.sh (启动脚本)"
echo "   - diary.service (systemd服务文件)"
echo "   - uploads/ (上传文件目录)"
echo ""
echo "🚀 部署步骤："
echo "1. 将 dist/ 目录上传到服务器"
echo "2. 修改 .env 文件中的域名配置"
echo "3. 修改前端 .env.production 中的域名配置并重新构建"
echo "4. 运行 ./start.sh 启动服务"
echo "5. 或者使用 systemd 管理服务（需要修改 diary.service 中的路径）"
